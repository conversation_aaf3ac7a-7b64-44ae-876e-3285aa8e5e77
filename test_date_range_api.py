#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断数据时间范围API
"""

import pandas as pd
import os
import json
from datetime import datetime

def find_latest_outage_file():
    """查找最新的频繁瞬断文件和对应的列映射"""
    current_dir = os.getcwd()

    # 查找所有Excel文件，优先使用用户上传的文件
    excel_files = []
    default_file = '7月至8.4频繁瞬断.xlsx'
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            file_path = os.path.join(current_dir, file)
            file_time = os.path.getmtime(file_path)
            
            # 设置优先级：原始瞬断告警数据 > 用户上传的瞬断文件 > 其他瞬断文件 > 默认文件 > 其他文件
            if '瞬断告警' in file:
                priority = 4  # 原始瞬断告警数据最高优先级
            elif '频繁瞬断' in file and file != default_file:
                priority = 3  # 用户上传的瞬断文件高优先级
            elif '频繁瞬断' in file and file == default_file:
                priority = 1  # 默认文件较低优先级
            elif '瞬断' in file:
                priority = 2  # 其他瞬断文件中等优先级
            else:
                priority = 0  # 其他文件最低优先级
                
            excel_files.append((file, file_time, priority))

    # 如果没有找到任何Excel文件
    if not excel_files:
        raise FileNotFoundError("没有找到频繁瞬断数据文件")

    # 按优先级和时间排序，获取最新文件
    excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
    latest_file = excel_files[0][0]
    
    print(f"选择的瞬断数据文件: {latest_file} (优先级: {excel_files[0][2]})")

    # 查找对应的列映射文件
    mapping_file = latest_file.rsplit('.', 1)[0] + '_mapping.json'
    column_mapping = None

    if os.path.exists(mapping_file):
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                column_mapping = json.load(f)
        except:
            column_mapping = None

    return latest_file, column_mapping

def test_get_outage_date_range():
    """测试获取瞬断数据时间范围"""
    print("🔍 测试获取瞬断数据时间范围")
    print("=" * 50)
    
    try:
        # 首先尝试从当前选择的瞬断文件获取时间范围
        try:
            latest_file, column_mapping = find_latest_outage_file()
            df = pd.read_excel(latest_file)
            print(f"✅ 使用瞬断数据文件: {latest_file}")
            print(f"   文件行数: {len(df)}")
            
            # 如果有列映射，重命名列
            if column_mapping:
                df = df.rename(columns={v: k for k, v in column_mapping.items()})
                print(f"✅ 应用列映射: {list(column_mapping.keys())}")
            
            # 兼容不同的时间列名
            time_column = None
            if '告警产生时间' in df.columns:
                time_column = '告警产生时间'
            elif '告警发生时间' in df.columns:
                time_column = '告警发生时间'
            
            print(f"🕐 检测到时间列: {time_column}")
            
            if time_column:
                df[time_column] = pd.to_datetime(df[time_column], errors='coerce')
                valid_dates = df[time_column].dropna()
                
                print(f"✅ 有效时间记录: {len(valid_dates)}/{len(df)}")
                
                if len(valid_dates) > 0:
                    start_date = valid_dates.min().strftime('%Y-%m-%d')
                    end_date = valid_dates.max().strftime('%Y-%m-%d')
                    
                    print(f"📅 数据时间范围:")
                    print(f"   开始日期: {start_date}")
                    print(f"   结束日期: {end_date}")
                    print(f"   跨度天数: {(valid_dates.max() - valid_dates.min()).days + 1}")
                    
                    # 显示时间分布
                    date_counts = valid_dates.dt.date.value_counts().sort_index()
                    print(f"\n📊 每日记录数分布:")
                    for date, count in date_counts.head(10).items():
                        print(f"   {date}: {count}条")
                    if len(date_counts) > 10:
                        print(f"   ... 还有{len(date_counts) - 10}天的数据")
                    
                    return {
                        'success': True,
                        'date_range': {
                            'start': start_date,
                            'end': end_date
                        },
                        'source': 'file',
                        'filename': latest_file,
                        'total_records': len(df),
                        'valid_time_records': len(valid_dates)
                    }
                else:
                    print("❌ 没有有效的时间记录")
                    return {'success': False, 'error': '没有有效的时间记录'}
            else:
                print("❌ 未找到时间列")
                return {'success': False, 'error': '未找到时间列'}
        
        except Exception as e:
            print(f"❌ 从文件获取时间范围失败: {str(e)}")
            return {'success': False, 'error': f'从文件获取失败: {str(e)}'}
    
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_default_dates():
    """测试默认日期设置"""
    print("\n🗓️  测试默认日期设置")
    print("=" * 50)
    
    # 模拟页面默认日期
    default_start = '2025-07-01'
    default_end = '2025-08-05'
    
    print(f"📅 当前页面默认日期:")
    print(f"   开始日期: {default_start}")
    print(f"   结束日期: {default_end}")
    
    # 获取实际数据时间范围
    result = test_get_outage_date_range()
    
    if result['success']:
        actual_start = result['date_range']['start']
        actual_end = result['date_range']['end']
        
        print(f"\n📊 实际数据时间范围:")
        print(f"   开始日期: {actual_start}")
        print(f"   结束日期: {actual_end}")
        
        # 比较差异
        if default_start != actual_start or default_end != actual_end:
            print(f"\n⚠️  日期不匹配，建议更新:")
            print(f"   建议开始日期: {actual_start}")
            print(f"   建议结束日期: {actual_end}")
            return False
        else:
            print(f"\n✅ 默认日期与实际数据匹配")
            return True
    else:
        print(f"\n❌ 无法获取实际数据时间范围")
        return False

def main():
    """主测试函数"""
    print("🧪 瞬断数据时间范围API测试")
    print("=" * 60)
    
    # 测试1: 获取时间范围
    result = test_get_outage_date_range()
    
    # 测试2: 默认日期比较
    dates_match = test_default_dates()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print("=" * 60)
    
    if result['success']:
        print("✅ 时间范围获取成功")
        print(f"   数据源: {result.get('source', '未知')}")
        print(f"   文件: {result.get('filename', '未知')}")
        print(f"   记录数: {result.get('total_records', 0)}")
        print(f"   时间范围: {result['date_range']['start']} 至 {result['date_range']['end']}")
        
        if dates_match:
            print("✅ 默认日期设置正确")
        else:
            print("⚠️  建议更新页面默认日期")
        
        print("\n💡 使用建议:")
        print("1. 页面会自动设置为数据的实际时间范围")
        print("2. 用户无需手动调整开始和结束日期")
        print("3. 系统会显示数据来源和记录数信息")
        
    else:
        print("❌ 时间范围获取失败")
        print(f"   错误: {result.get('error', '未知错误')}")
        print("\n💡 建议:")
        print("1. 检查瞬断数据文件是否存在")
        print("2. 确认文件包含有效的时间列")
        print("3. 验证文件格式是否正确")

if __name__ == "__main__":
    main()
