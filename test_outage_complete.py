#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试瞬断模块功能
"""

import os
import pandas as pd
import requests
import json
from datetime import datetime, timed<PERSON>ta

def test_api_call():
    """测试瞬断分析API调用"""
    print("🌐 测试瞬断分析API调用...")
    print("=" * 50)
    
    # API端点
    url = "http://localhost:5000/api/frequent_outage/analyze"
    
    # 测试数据
    test_data = {
        "start_date": "2025-07-01",
        "end_date": "2025-08-05"
    }
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"📊 请求数据: {test_data}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📈 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ API调用成功!")
                
                # 分析结果
                records = result.get('records', [])
                summary = result.get('summary', {})
                
                print(f"📊 分析结果:")
                print(f"   总记录数: {len(records)}")
                print(f"   总瞬断次数: {summary.get('total_outages', 0)}")
                print(f"   涉及站点数: {summary.get('total_sites', 0)}")
                
                # 显示前5条记录
                if records:
                    print(f"\n📋 前5条记录:")
                    for i, record in enumerate(records[:5], 1):
                        print(f"   {i}. {record.get('小区名称', 'N/A')}")
                        print(f"      网格: {record.get('归属网格', 'N/A')}")
                        print(f"      瞬断次数: {record.get('瞬断次数', 0)}")
                        print(f"      瞬断天数: {record.get('瞬断天数', 0)}")
                        print()
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到服务器")
        print("💡 请确保Web应用正在运行 (python web_app.py)")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def test_file_priority():
    """测试文件优先级"""
    print("\n🔍 测试文件优先级...")
    print("=" * 50)
    
    current_dir = os.getcwd()
    excel_files = []
    default_file = '7月至8.4频繁瞬断.xlsx'
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            file_path = os.path.join(current_dir, file)
            file_time = os.path.getmtime(file_path)
            
            # 设置优先级：用户上传的瞬断文件 > 其他瞬断文件 > 默认文件 > 其他文件
            if '频繁瞬断' in file and file != default_file:
                priority = 3  # 用户上传的瞬断文件最高优先级
            elif '频繁瞬断' in file and file == default_file:
                priority = 1  # 默认文件较低优先级
            elif '瞬断' in file:
                priority = 2  # 其他瞬断文件中等优先级
            else:
                priority = 0  # 其他文件最低优先级
                
            excel_files.append((file, file_time, priority))
    
    if excel_files:
        # 按优先级和时间排序
        excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
        selected_file = excel_files[0][0]
        
        print(f"✅ 系统将选择: {selected_file}")
        print(f"   优先级: {excel_files[0][2]}")
        
        # 检查是否是默认文件
        if selected_file == default_file:
            print("❌ 警告: 仍在使用默认文件!")
            print("💡 建议: 确保默认文件已被删除")
            return False
        else:
            print("✅ 确认: 使用用户上传的文件")
            return True
    else:
        print("❌ 未找到任何Excel文件")
        return False

def test_data_format():
    """测试数据格式兼容性"""
    print("\n📄 测试数据格式兼容性...")
    print("=" * 50)
    
    # 测试原始瞬断告警数据
    if os.path.exists('8月瞬断告警.xlsx'):
        try:
            df = pd.read_excel('8月瞬断告警.xlsx')
            print(f"✅ 原始瞬断数据读取成功")
            print(f"   文件: 8月瞬断告警.xlsx")
            print(f"   行数: {len(df):,}")
            
            # 检查关键列
            required_columns = ['网元名称', '告警发生时间']
            missing_columns = []
            
            for col in required_columns:
                if col not in df.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"⚠️  缺失关键列: {missing_columns}")
            else:
                print(f"✅ 包含所有关键列")
                
                # 测试时间处理
                if '告警发生时间' in df.columns:
                    df['告警发生时间'] = pd.to_datetime(df['告警发生时间'], errors='coerce')
                    valid_times = df['告警发生时间'].notna().sum()
                    print(f"✅ 有效时间记录: {valid_times:,}/{len(df):,}")
                
                # 测试网元名称
                if '网元名称' in df.columns:
                    unique_cells = df['网元名称'].nunique()
                    print(f"✅ 唯一网元数量: {unique_cells:,}")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取原始瞬断数据失败: {str(e)}")
            return False
    else:
        print("⚠️  未找到原始瞬断数据文件: 8月瞬断告警.xlsx")
        return False

def main():
    """主测试函数"""
    print("🧪 瞬断模块完整功能测试")
    print("=" * 60)
    print("目标: 验证瞬断模块是否正确使用用户导入的数据")
    print("=" * 60)
    
    # 测试结果
    results = {}
    
    # 测试1: 文件优先级
    results['file_priority'] = test_file_priority()
    
    # 测试2: 数据格式
    results['data_format'] = test_data_format()
    
    # 测试3: API调用（需要服务器运行）
    print("\n⚠️  API测试需要Web服务器运行")
    print("如果服务器未运行，请先执行: python web_app.py")
    
    user_input = input("\n是否进行API测试? (y/n): ").lower().strip()
    if user_input == 'y':
        results['api_call'] = test_api_call()
    else:
        results['api_call'] = None
        print("⏭️  跳过API测试")
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = 0
    
    for test_name, result in results.items():
        if result is not None:
            total_tests += 1
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        else:
            print(f"⏭️  {test_name}: 跳过")
    
    print(f"\n📊 测试统计: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests and total_tests > 0:
        print("\n🎉 所有测试通过!")
        print("✅ 瞬断模块已正确配置为使用用户导入的数据")
        
        print("\n💡 使用建议:")
        print("1. 瞬断模块现在会优先使用你上传的数据")
        print("2. 默认数据已被删除并备份")
        print("3. 可以正常使用瞬断分析功能")
        print("4. 如需恢复默认数据，可从backup_default_files目录恢复")
        
    else:
        print("\n⚠️  部分测试失败")
        print("💡 建议:")
        print("1. 检查瞬断数据文件是否正确")
        print("2. 确保Web服务器正常运行")
        print("3. 检查文件权限和路径")

if __name__ == "__main__":
    main()
