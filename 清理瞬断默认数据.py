#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理瞬断模块的默认数据
只删除瞬断相关的默认文件，其他模块数据保持不变
"""

import os
import shutil
from datetime import datetime

def remove_outage_default_files():
    """删除瞬断模块的默认数据文件"""
    print("🗑️  清理瞬断模块默认数据...")
    print("=" * 50)
    
    # 创建备份目录
    backup_dir = 'backup_outage_default_data'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"✅ 创建备份目录: {backup_dir}")
    
    # 只删除瞬断相关的默认文件
    outage_files = [
        # 原始瞬断告警数据
        '8月瞬断告警.xlsx',
        
        # 瞬断分析结果文件
        '频繁瞬断分析_包含天数.xlsx',
        '频繁瞬断分析_包含最近日期.xlsx',
        '频繁瞬断分析_居中对齐.xlsx',
        '频繁瞬断分析_日期加粗.xlsx',
        
        # 可能存在的其他瞬断文件
        '7月至8.4频繁瞬断.xlsx',  # 如果还有残留
        '瞬断告警.xlsx',
        '频繁瞬断.xlsx',
        '瞬断数据.xlsx',
        
        # 瞬断相关的映射文件
        '8月瞬断告警_mapping.json',
        '频繁瞬断分析_mapping.json',
        '瞬断告警_mapping.json'
    ]
    
    removed_files = []
    backed_up_files = []
    
    print(f"🔍 检查瞬断相关文件...")
    
    for file_name in outage_files:
        if os.path.exists(file_name):
            try:
                # 备份文件
                backup_path = os.path.join(backup_dir, f"{file_name}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                shutil.copy2(file_name, backup_path)
                backed_up_files.append((file_name, backup_path))
                print(f"📦 备份: {file_name}")
                
                # 删除原文件
                os.remove(file_name)
                removed_files.append(file_name)
                print(f"🗑️  删除: {file_name}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_name} 时出错: {str(e)}")
        else:
            print(f"ℹ️  文件不存在: {file_name}")
    
    # 检查是否还有其他瞬断相关文件
    print(f"\n🔍 扫描其他可能的瞬断文件...")
    current_dir = os.getcwd()
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            if any(keyword in file for keyword in ['瞬断', '频繁瞬断']):
                if file not in [f for f, _ in backed_up_files] and file not in removed_files:
                    print(f"⚠️  发现其他瞬断文件: {file}")
                    user_input = input(f"是否删除 {file}? (y/n): ").lower().strip()
                    if user_input == 'y':
                        try:
                            backup_path = os.path.join(backup_dir, f"{file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                            shutil.copy2(file, backup_path)
                            os.remove(file)
                            removed_files.append(file)
                            backed_up_files.append((file, backup_path))
                            print(f"🗑️  删除: {file}")
                        except Exception as e:
                            print(f"❌ 删除 {file} 失败: {str(e)}")
    
    print(f"\n📊 瞬断文件清理结果:")
    print(f"   删除文件: {len(removed_files)} 个")
    print(f"   备份文件: {len(backed_up_files)} 个")
    
    if removed_files:
        print(f"\n🗑️  已删除的瞬断文件:")
        for file_name in removed_files:
            print(f"   - {file_name}")
    
    return len(removed_files)

def update_outage_config():
    """更新瞬断模块配置"""
    print(f"\n⚙️  更新瞬断模块配置...")
    print("=" * 50)
    
    try:
        # 检查find_latest_outage_file函数是否已经正确配置
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有正确的错误处理
        if '没有找到瞬断数据文件，请上传瞬断告警数据文件' in content:
            print(f"✅ 瞬断模块错误提示已配置")
        else:
            print(f"⚠️  瞬断模块错误提示需要更新")
        
        # 检查文件优先级设置
        if '瞬断告警' in content and 'priority = 4' in content:
            print(f"✅ 瞬断文件优先级已正确设置")
        else:
            print(f"⚠️  瞬断文件优先级可能需要调整")
        
        print(f"✅ 瞬断模块配置检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {str(e)}")
        return False

def verify_clean_state():
    """验证清理后的状态"""
    print(f"\n🔍 验证清理状态...")
    print("=" * 50)
    
    current_dir = os.getcwd()
    remaining_outage_files = []
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            if any(keyword in file for keyword in ['瞬断', '频繁瞬断']):
                remaining_outage_files.append(file)
    
    if remaining_outage_files:
        print(f"⚠️  仍有瞬断文件存在:")
        for file in remaining_outage_files:
            print(f"   - {file}")
        print(f"💡 这些文件可能是用户数据，已保留")
    else:
        print(f"✅ 所有默认瞬断文件已清理完成")
        print(f"✅ 系统现在不会使用任何默认瞬断数据")
    
    return len(remaining_outage_files) == 0

def main():
    """主清理函数"""
    print("🧹 清理瞬断模块默认数据")
    print("=" * 60)
    print("目标: 只删除瞬断模块的默认数据，其他模块数据保持不变")
    print("=" * 60)
    
    # 确认操作
    print("📋 将要清理的内容:")
    print("   ✅ 瞬断告警原始数据文件")
    print("   ✅ 频繁瞬断分析结果文件")
    print("   ✅ 瞬断相关的映射文件")
    print("   ❌ 扇区数据文件 (保留)")
    print("   ❌ 网格数据文件 (保留)")
    print("   ❌ 数据库内容 (保留)")
    print("   ❌ 其他模块数据 (保留)")
    
    user_input = input("\n确认只清理瞬断模块数据? (y/n): ").lower().strip()
    
    if user_input != 'y':
        print("❌ 操作已取消")
        return
    
    print("\n🚀 开始清理瞬断默认数据...")
    
    # 步骤1: 删除瞬断文件
    files_removed = remove_outage_default_files()
    
    # 步骤2: 更新配置
    config_updated = update_outage_config()
    
    # 步骤3: 验证状态
    clean_state = verify_clean_state()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 瞬断模块清理完成:")
    print("=" * 60)
    
    print(f"✅ 删除瞬断文件: {files_removed} 个")
    print(f"✅ 配置检查: {'通过' if config_updated else '需要手动检查'}")
    print(f"✅ 清理状态: {'完全干净' if clean_state else '部分文件保留'}")
    
    if files_removed > 0:
        print(f"\n🎉 瞬断模块清理完成!")
        print(f"✅ 瞬断模块现在不会使用任何默认数据")
        print(f"✅ 其他模块数据完全保留")
        print(f"✅ 所有删除的文件都已备份")
        
        print(f"\n💡 现在瞬断模块的使用方式:")
        print(f"1. 📤 在瞬断分析页面上传你的瞬断告警文件")
        print(f"2. 🎯 系统会自动识别时间范围并分析")
        print(f"3. 📊 获得基于你实际数据的分析结果")
        print(f"4. 🔄 如需恢复默认文件，可从backup_outage_default_data目录恢复")
        
        print(f"\n🚀 重启应用生效:")
        print(f"   python web_app.py")
        
    else:
        print(f"\n✅ 瞬断模块已经是干净状态")
        print(f"ℹ️  没有找到需要清理的默认瞬断数据")
    
    print(f"\n📁 其他模块数据状态:")
    print(f"   ✅ 扇区数据: 保持不变")
    print(f"   ✅ 网格数据: 保持不变") 
    print(f"   ✅ 数据库: 保持不变")
    print(f"   ✅ 上传缓存: 保持不变")

if __name__ == "__main__":
    main()
