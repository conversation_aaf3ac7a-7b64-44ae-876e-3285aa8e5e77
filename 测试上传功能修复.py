#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断文件上传功能修复
"""

import requests
import os
import time

def test_upload_api():
    """测试上传API"""
    print("🧪 测试瞬断文件上传API")
    print("=" * 50)
    
    # 检查服务器状态
    try:
        response = requests.get('http://localhost:5000', timeout=2)
        print("✅ Web服务器正在运行")
    except:
        print("❌ Web服务器未运行")
        print("💡 请先启动: python web_app.py")
        return False
    
    # 创建一个测试文件
    test_filename = 'test_outage_upload.xlsx'
    
    if not os.path.exists(test_filename):
        print(f"📄 创建测试文件: {test_filename}")
        
        import pandas as pd
        
        # 创建测试数据
        test_data = {
            '网元名称': [
                'HZMD0001-ZX-S3H-(牡丹区测试站点1)',
                'HZMD0002-ZX-S3H-(牡丹区测试站点2)',
                'HZCX0001-ZX-S3H-(曹县测试站点1)',
                'HZCX0002-ZX-S3H-(曹县测试站点2)',
                'HZJY0001-ZX-S3H-(巨野测试站点1)'
            ],
            '告警发生时间': [
                '2025-08-14 10:30:00',
                '2025-08-14 11:15:00',
                '2025-08-14 12:00:00',
                '2025-08-14 13:45:00',
                '2025-08-14 14:20:00'
            ],
            '网管设备类型': ['NRcell', 'eNodeB', 'NRcell', 'eNodeB', 'NRcell'],
            '是否为室分系统信号源': ['否', '是', '否', '否', '是']
        }
        
        df = pd.DataFrame(test_data)
        df.to_excel(test_filename, index=False)
        print(f"✅ 测试文件创建完成: {len(df)}条记录")
    
    # 测试上传
    try:
        url = "http://localhost:5000/api/frequent_outage/upload"
        
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            print(f"📡 上传到: {url}")
            print(f"⏳ 正在上传...")
            
            response = requests.post(url, files=files, timeout=60)
            
            print(f"📈 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print("✅ 上传成功!")
                    
                    print(f"📊 上传结果:")
                    print(f"   文件名: {result.get('filename', 'N/A')}")
                    print(f"   识别列: {result.get('columns', [])}")
                    
                    # 检查日期范围
                    if result.get('date_range'):
                        date_range = result['date_range']
                        print(f"📅 时间范围:")
                        print(f"   开始: {date_range.get('start', 'N/A')}")
                        print(f"   结束: {date_range.get('end', 'N/A')}")
                    
                    # 检查分析结果
                    if result.get('analysis_data'):
                        analysis = result['analysis_data']
                        records = analysis.get('records', [])
                        summary = analysis.get('summary', {})
                        
                        print(f"📊 自动分析结果:")
                        print(f"   分析记录: {len(records)}")
                        print(f"   瞬断次数: {summary.get('total_outages', 0)}")
                        print(f"   涉及站点: {summary.get('total_sites', 0)}")
                        
                        if records:
                            print(f"📋 前3条分析结果:")
                            for i, record in enumerate(records[:3], 1):
                                print(f"   {i}. {record.get('小区名称', 'N/A')}")
                                print(f"      网格: {record.get('归属网格', 'N/A')}")
                                print(f"      瞬断次数: {record.get('瞬断次数', 0)}")
                    
                    elif result.get('analysis_error'):
                        print(f"⚠️  上传成功但分析失败:")
                        print(f"   错误: {result['analysis_error']}")
                    
                    return True
                else:
                    print(f"❌ 上传失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传测试失败: {str(e)}")
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🗑️  清理测试文件: {test_filename}")

def test_page_upload():
    """测试页面上传功能"""
    print(f"\n🌐 测试页面上传功能...")
    print("=" * 50)
    
    try:
        # 检查页面是否正常
        response = requests.get('http://localhost:5000/frequent_outage_analyzer', timeout=10)
        
        if response.status_code == 200:
            print("✅ 瞬断分析页面访问正常")
            
            content = response.text
            
            # 检查关键元素
            checks = [
                ('上传区域', 'uploadArea' in content),
                ('文件输入', 'fileUpload' in content),
                ('上传函数', 'handleFileUpload' in content),
                ('分析函数', 'uploadAndAnalyze' in content),
                ('加载元素', 'id="loading"' in content),
                ('结果元素', 'id="results"' in content),
                ('显示函数', 'displayResults' in content)
            ]
            
            print(f"📋 页面元素检查:")
            all_ok = True
            for check_name, exists in checks:
                status = "✅" if exists else "❌"
                print(f"   {status} {check_name}")
                if not exists:
                    all_ok = False
            
            return all_ok
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面测试失败: {str(e)}")
        return False

def check_console_errors():
    """检查可能的控制台错误"""
    print(f"\n🔍 检查常见问题...")
    print("=" * 50)
    
    issues = []
    
    # 检查模板文件
    template_file = 'templates/frequent_outage_analyzer.html'
    if os.path.exists(template_file):
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查可能的问题
        if 'loadingSpinner' in content:
            issues.append("❌ 仍有 loadingSpinner 引用")
        else:
            print("✅ loadingSpinner 引用已修复")
        
        if 'handleFileUpload' in content and 'uploadAndAnalyze' in content:
            print("✅ 上传函数存在")
        else:
            issues.append("❌ 上传函数缺失")
        
        if 'displayResults' in content:
            print("✅ 显示结果函数存在")
        else:
            issues.append("❌ 显示结果函数缺失")
    
    # 检查API路由
    web_app_file = 'web_app.py'
    if os.path.exists(web_app_file):
        with open(web_app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '/api/frequent_outage/upload' in content:
            print("✅ 上传API路由存在")
        else:
            issues.append("❌ 上传API路由缺失")
        
        if 'upload_frequent_outage_file' in content:
            print("✅ 上传处理函数存在")
        else:
            issues.append("❌ 上传处理函数缺失")
    
    if issues:
        print(f"\n⚠️  发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 未发现明显问题")
        return True

def main():
    """主测试函数"""
    print("🔧 瞬断文件上传功能修复测试")
    print("=" * 60)
    
    # 检查常见问题
    no_issues = check_console_errors()
    
    # 测试页面功能
    page_ok = test_page_upload()
    
    # 测试API上传
    if no_issues and page_ok:
        print(f"\n❓ 是否测试实际文件上传？")
        print(f"⚠️  这会创建测试文件并上传到服务器")
        user_input = input("输入 y 继续，其他键跳过: ").lower().strip()
        
        if user_input == 'y':
            upload_ok = test_upload_api()
        else:
            upload_ok = None
            print("⏭️  跳过上传测试")
    else:
        upload_ok = None
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 修复测试结果:")
    print("=" * 60)
    
    if no_issues and page_ok:
        print("✅ 页面功能修复成功")
        print("✅ 所有必要元素都存在")
        print("✅ JavaScript函数正确引用")
        
        if upload_ok:
            print("✅ 文件上传功能测试通过")
        elif upload_ok is None:
            print("⏭️  文件上传功能未测试")
        else:
            print("❌ 文件上传功能测试失败")
        
        print(f"\n💡 现在可以尝试:")
        print(f"1. 打开瞬断分析页面")
        print(f"2. 点击上传区域选择Excel文件")
        print(f"3. 或直接拖拽Excel文件到上传区域")
        print(f"4. 查看上传和分析结果")
        
    else:
        print("❌ 仍有问题需要解决")
        if not no_issues:
            print("❌ 代码中存在问题")
        if not page_ok:
            print("❌ 页面功能不完整")
        
        print(f"\n💡 建议:")
        print(f"1. 检查控制台错误信息")
        print(f"2. 确保Web服务器正常运行")
        print(f"3. 刷新页面重试")

if __name__ == "__main__":
    main()
