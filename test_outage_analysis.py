#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断分析完整流程
"""

import pandas as pd
import os
import json
from datetime import datetime

def detect_column_mapping(columns):
    """智能检测列名映射"""
    column_mapping = {}

    # 直接检查是否有必要的列名
    for col in columns:
        col_str = str(col).strip()

        # 网元名称 - 包含匹配
        if '网元名称' in col_str or col_str in ['小区名称', '基站名称', '站点名称']:
            if '网元名称' not in column_mapping:
                column_mapping['网元名称'] = col

        # 告警时间 - 支持发生、发现、产生时间
        elif (('告警' in col_str and ('发生' in col_str or '发现' in col_str or '产生' in col_str)) or
              col_str in ['故障发生时间', '开始时间', '告警发生时间', '告警发现时间', '告警产生时间']):
            if '告警产生时间' not in column_mapping:
                column_mapping['告警产生时间'] = col

        # 告警清除时间 - 包含匹配
        elif ('告警' in col_str and '清除' in col_str) or col_str in ['告警消失时间', '结束时间', '清除时间']:
            if '告警清除时间' not in column_mapping:
                column_mapping['告警清除时间'] = col

        # 室分信息 - 包含匹配
        elif '室分' in col_str or col_str in ['是否为室分系统信号源', '室分标识', '覆盖类型']:
            if '是否为室分系统信号源' not in column_mapping:
                column_mapping['是否为室分系统信号源'] = col

        # 网络制式 - 包含匹配
        elif '设备类型' in col_str or col_str in ['网络制式', '制式']:
            if '网管设备类型' not in column_mapping:
                column_mapping['网管设备类型'] = col

    # 检查必要字段
    required_fields = ['网元名称', '告警产生时间']
    for field in required_fields:
        if field not in column_mapping:
            print(f"缺少必要字段: {field}")
            return None

    return column_mapping

def process_frequent_outage_data_simple(df):
    """简化的瞬断数据处理函数"""
    results = []
    
    if df.empty:
        return {'records': [], 'summary': {'total_outages': 0, 'total_sites': 0}}
    
    # 按网元名称分组，统计详细的瞬断信息
    grouped_data = {}
    
    for _, row in df.iterrows():
        network_name = str(row.get('网元名称', ''))
        if not network_name or network_name == 'nan':
            continue
        
        # 获取网络制式
        network_type = str(row.get('网管设备类型', '未知'))
        
        # 获取室分信息
        indoor_info = str(row.get('是否为室分系统信号源', ''))
        
        if network_name not in grouped_data:
            grouped_data[network_name] = {
                '网络制式': network_type,
                '瞬断次数': 0,
                '瞬断详情': [],
                '是否室分': indoor_info
            }
        
        grouped_data[network_name]['瞬断次数'] += 1
        
        # 收集详细的瞬断信息
        try:
            if pd.notna(row.get('告警产生时间')):
                date_obj = pd.to_datetime(row['告警产生时间'])
                formatted_date = date_obj.strftime('%m-%d')
                formatted_start_time = date_obj.strftime('%H:%M')
                
                grouped_data[network_name]['瞬断详情'].append({
                    '日期': formatted_date,
                    '开始时间': formatted_start_time,
                    '时间段': formatted_start_time
                })
        except:
            grouped_data[network_name]['瞬断详情'].append({
                '日期': '未知日期',
                '开始时间': '未知时间',
                '时间段': '未知'
            })
    
    # 转换为结果格式
    for network_name, data in grouped_data.items():
        # 简化网格匹配（这里可以扩展为更复杂的匹配逻辑）
        grid_name = '未知网格'
        if '牡丹区' in network_name or 'HZMD' in network_name:
            grid_name = '牡丹区'
        elif '曹县' in network_name or 'HZCX' in network_name:
            grid_name = '曹县'
        elif '单县' in network_name or 'HZSX' in network_name:
            grid_name = '单县'
        elif '成武' in network_name or 'HZCW' in network_name:
            grid_name = '成武县'
        elif '定陶' in network_name or 'HZDT' in network_name:
            grid_name = '定陶区'
        elif '东明' in network_name or 'HZDM' in network_name:
            grid_name = '东明县'
        elif '巨野' in network_name or 'HZJY' in network_name:
            grid_name = '巨野县'
        elif '鄄城' in network_name or 'HZJC' in network_name:
            grid_name = '鄄城县'
        elif '郓城' in network_name or 'HZYC' in network_name:
            grid_name = '郓城县'
        elif '鲁西' in network_name or 'HZKF' in network_name:
            grid_name = '鲁西新区'
        
        # 计算瞬断天数
        unique_dates = set()
        for detail in data['瞬断详情']:
            unique_dates.add(detail['日期'])
        outage_days = len(unique_dates)
        
        # 找到最近的瞬断日期
        latest_date_str = '未知'
        if unique_dates:
            # 简化处理，取第一个日期
            latest_date_str = sorted(unique_dates)[-1]
        
        # 生成详细故障分布
        date_str = f"共{data['瞬断次数']}次"
        
        # 判断是否室分
        is_indoor = '未知'
        if '是' in data['是否室分']:
            is_indoor = '是'
        elif '否' in data['是否室分']:
            is_indoor = '否'
        
        result = {
            '归属网格': grid_name,
            '小区名称': network_name,
            '瞬断次数': data['瞬断次数'],
            '瞬断天数': outage_days,
            '最近瞬断日期': latest_date_str,
            '网络制式': data['网络制式'],
            '是否室分': is_indoor,
            '详细故障分布': date_str
        }
        results.append(result)
    
    # 按瞬断次数降序排序
    results.sort(key=lambda x: x['瞬断次数'], reverse=True)
    
    # 生成摘要
    total_outages = sum(r['瞬断次数'] for r in results)
    total_sites = len(results)
    
    return {
        'records': results,
        'summary': {
            'total_outages': total_outages,
            'total_sites': total_sites
        }
    }

def test_outage_analysis():
    """测试瞬断分析完整流程"""
    print("🧪 测试瞬断分析完整流程")
    print("=" * 60)
    
    # 步骤1: 文件选择
    print("📁 步骤1: 文件选择")
    filename = '8月瞬断告警.xlsx'
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    print(f"✅ 选择文件: {filename}")
    
    # 步骤2: 读取文件
    print(f"\n📄 步骤2: 读取文件")
    try:
        df = pd.read_excel(filename)
        print(f"✅ 文件读取成功，行数: {len(df)}")
    except Exception as e:
        print(f"❌ 文件读取失败: {str(e)}")
        return False
    
    # 步骤3: 列映射检测
    print(f"\n🔍 步骤3: 列映射检测")
    column_mapping = detect_column_mapping(df.columns)
    
    if not column_mapping:
        print(f"❌ 列映射检测失败")
        return False
    
    print(f"✅ 列映射检测成功")
    for key, value in column_mapping.items():
        print(f"   {key} -> {value}")
    
    # 步骤4: 数据预处理
    print(f"\n⚙️  步骤4: 数据预处理")
    try:
        # 重命名列
        df_processed = df.rename(columns={v: k for k, v in column_mapping.items()})
        
        # 处理时间列
        if '告警产生时间' in df_processed.columns:
            df_processed['告警产生时间'] = pd.to_datetime(df_processed['告警产生时间'], errors='coerce')
            df_processed['日期'] = df_processed['告警产生时间'].dt.date
            df_processed['时间'] = df_processed['告警产生时间'].dt.time
            
            valid_times = df_processed['告警产生时间'].notna().sum()
            print(f"✅ 时间处理完成，有效记录: {valid_times}/{len(df_processed)}")
        
        print(f"✅ 数据预处理完成")
        
    except Exception as e:
        print(f"❌ 数据预处理失败: {str(e)}")
        return False
    
    # 步骤5: 瞬断分析
    print(f"\n📊 步骤5: 瞬断分析")
    try:
        analysis_result = process_frequent_outage_data_simple(df_processed)
        
        records = analysis_result['records']
        summary = analysis_result['summary']
        
        print(f"✅ 瞬断分析完成")
        print(f"   总记录数: {len(records)}")
        print(f"   总瞬断次数: {summary['total_outages']}")
        print(f"   涉及站点数: {summary['total_sites']}")
        
        # 显示前5条记录
        if records:
            print(f"\n📋 前5条分析结果:")
            for i, record in enumerate(records[:5], 1):
                print(f"   {i}. {record['小区名称'][:30]}...")
                print(f"      网格: {record['归属网格']}")
                print(f"      瞬断次数: {record['瞬断次数']}")
                print(f"      瞬断天数: {record['瞬断天数']}")
                print(f"      网络制式: {record['网络制式']}")
                print(f"      是否室分: {record['是否室分']}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 瞬断分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_outage_analysis()
    
    print("=" * 60)
    if success:
        print("🎉 瞬断分析测试成功!")
        print("✅ 所有步骤都正常工作")
        print("💡 现在可以在Web界面中正常使用瞬断分析功能")
    else:
        print("❌ 瞬断分析测试失败")
        print("💡 请检查文件格式和数据内容")

if __name__ == "__main__":
    main()
