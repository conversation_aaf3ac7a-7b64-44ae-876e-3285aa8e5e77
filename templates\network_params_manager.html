<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工参管理 - 菏泽数据提取工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 30px;
        }

        .tab {
            padding: 15px 30px;
            background: none;
            border: none;
            font-size: 1.1rem;
            font-weight: 600;
            color: #6c757d;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #4facfe;
            border-bottom-color: #4facfe;
        }

        .tab:hover {
            color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-card .date {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            text-align: center;
        }

        .upload-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .upload-form {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            min-width: 200px;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tbody tr:hover {
            background: rgba(79, 172, 254, 0.05);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .history-table {
            margin-top: 30px;
        }

        .history-table h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                text-align: center;
                border-bottom: 1px solid #dee2e6;
                border-right: none;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .search-form {
                flex-direction: column;
                align-items: stretch;
            }

            .table-container {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="btn btn-secondary back-btn">← 返回首页</a>
    
    <div class="container">
        <div class="header">
            <h1>🔧 工参管理</h1>
            <p>4G/5G网络工程参数导入与查询管理</p>
        </div>

        <div class="main-content">
            <!-- 统计信息 -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <h3>4G工参数量</h3>
                    <div class="number" id="count4g">-</div>
                    <div class="date" id="latest4g">暂无数据</div>
                </div>
                <div class="stat-card">
                    <h3>5G工参数量</h3>
                    <div class="number" id="count5g">-</div>
                    <div class="date" id="latest5g">暂无数据</div>
                </div>
            </div>

            <!-- 选项卡 -->
            <div class="tabs">
                <button class="tab active" onclick="switchTab('import')">📤 导入工参</button>
                <button class="tab" onclick="switchTab('search')">🔍 查询工参</button>
                <button class="tab" onclick="switchTab('history')">📋 导入历史</button>
            </div>

            <!-- 导入工参 -->
            <div id="importTab" class="tab-content active">
                <div class="upload-section">
                    <h3>📤 上传工参文件</h3>
                    <form id="uploadForm" class="upload-form" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="paramType">工参类型</label>
                            <select id="paramType" name="type" class="form-control" required>
                                <option value="4g">4G工参</option>
                                <option value="5g">5G工参</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fileDate">文件日期</label>
                            <input type="date" id="fileDate" name="file_date" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="paramFile">选择Excel文件</label>
                            <input type="file" id="paramFile" name="file" class="form-control" accept=".xlsx,.xls" required>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <span id="uploadBtnText">开始导入</span>
                        </button>
                    </form>
                </div>

                <div id="uploadResult" style="display: none;"></div>
            </div>

            <!-- 查询工参 -->
            <div id="searchTab" class="tab-content">
                <div class="search-section">
                    <h3>🔍 工参查询</h3>
                    <div class="search-form">
                        <div class="form-group">
                            <label for="searchType">工参类型</label>
                            <select id="searchType" class="form-control" onchange="updateSearchColumns()">
                                <option value="4g">4G工参</option>
                                <option value="5g">5G工参</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="searchColumn">查询字段</label>
                            <select id="searchColumn" class="form-control">
                                <option value="all">全部字段</option>
                                <option value="cell_id">小区ID</option>
                                <option value="cell_name">小区名称</option>
                                <option value="site_id">基站ID</option>
                                <option value="district">地市</option>
                                <option value="county">县区</option>
                                <option value="scenario">场景</option>
                                <option value="band">频段</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="searchTerm">搜索关键词</label>
                            <input type="text" id="searchTerm" class="form-control" placeholder="输入搜索关键词">
                        </div>

                        <div class="form-group">
                            <label for="pageSize">每页显示</label>
                            <select id="pageSize" class="form-control">
                                <option value="20">20条</option>
                                <option value="50" selected>50条</option>
                                <option value="100">100条</option>
                                <option value="500">500条</option>
                                <option value="1000">1000条</option>
                                <option value="all">全部数据</option>
                            </select>
                        </div>

                        <button type="button" class="btn btn-primary" onclick="searchParams()">搜索</button>
                        <button type="button" class="btn btn-secondary" onclick="clearSearch()">清空</button>
                    </div>
                </div>

                <div id="searchResults">
                    <div class="loading" id="searchLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <div>正在查询...</div>
                    </div>

                    <div id="searchTable" style="display: none;">
                        <div class="table-container">
                            <table class="table">
                                <thead id="searchTableHead">
                                    <!-- 动态生成表头 -->
                                </thead>
                                <tbody id="searchTableBody">
                                    <!-- 动态生成表格内容 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination" id="searchPagination">
                            <!-- 动态生成分页 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导入历史 -->
            <div id="historyTab" class="tab-content">
                <div class="history-table">
                    <h3>📋 导入历史记录</h3>
                    <div class="loading" id="historyLoading">
                        <div class="loading-spinner"></div>
                        <div>正在加载...</div>
                    </div>

                    <div id="historyTable" style="display: none;">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>类型</th>
                                        <th>文件名</th>
                                        <th>文件日期</th>
                                        <th>总记录数</th>
                                        <th>成功导入</th>
                                        <th>错误记录</th>
                                        <th>耗时(秒)</th>
                                        <th>导入时间</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <!-- 动态生成内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let currentSearchData = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadHistory();

            // 设置默认日期为今天
            document.getElementById('fileDate').value = new Date().toISOString().split('T')[0];

            // 绑定上传表单事件
            document.getElementById('uploadForm').addEventListener('submit', handleUpload);
        });

        // 切换选项卡
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有选项卡的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的选项卡内容
            document.getElementById(tabName + 'Tab').classList.add('active');

            // 添加选中选项卡的active类
            event.target.classList.add('active');

            // 如果切换到历史记录选项卡，重新加载数据
            if (tabName === 'history') {
                loadHistory();
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/network_params/stats');
                const result = await response.json();

                if (result.success) {
                    const data = result.data;

                    // 更新4G统计
                    document.getElementById('count4g').textContent = data['4g'].count.toLocaleString();
                    document.getElementById('latest4g').textContent = data['4g'].latest_date || '暂无数据';

                    // 更新5G统计
                    document.getElementById('count5g').textContent = data['5g'].count.toLocaleString();
                    document.getElementById('latest5g').textContent = data['5g'].latest_date || '暂无数据';
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 处理文件上传
        async function handleUpload(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const uploadBtn = document.getElementById('uploadBtnText');
            const resultDiv = document.getElementById('uploadResult');

            // 显示上传状态
            uploadBtn.textContent = '正在导入...';
            event.target.querySelector('button').disabled = true;
            resultDiv.style.display = 'none';

            try {
                const response = await fetch('/api/network_params/import', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // 显示结果
                resultDiv.style.display = 'block';
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ 导入成功！</h4>
                            <p><strong>总记录数：</strong>${result.total_records.toLocaleString()}</p>
                            <p><strong>成功导入：</strong>${result.success_records.toLocaleString()}</p>
                            <p><strong>错误记录：</strong>${result.error_records}</p>
                            <p><strong>耗时：</strong>${result.import_duration}秒</p>
                        </div>
                    `;

                    // 重新加载统计信息
                    loadStats();

                    // 重置表单
                    event.target.reset();
                    document.getElementById('fileDate').value = new Date().toISOString().split('T')[0];
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>❌ 导入失败</h4>
                            <p>${result.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ 导入失败</h4>
                        <p>网络错误：${error.message}</p>
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                uploadBtn.textContent = '开始导入';
                event.target.querySelector('button').disabled = false;
            }
        }

        // 更新搜索字段选项
        function updateSearchColumns() {
            const searchType = document.getElementById('searchType').value;
            const searchColumn = document.getElementById('searchColumn');

            // 清空现有选项
            searchColumn.innerHTML = '';

            // 添加通用选项
            searchColumn.innerHTML += '<option value="all">全部字段</option>';

            if (searchType === '4g') {
                searchColumn.innerHTML += `
                    <option value="cell_id">小区ID</option>
                    <option value="cell_name">小区名称</option>
                    <option value="site_id">基站ID</option>
                    <option value="district">地市</option>
                    <option value="county">县区</option>
                    <option value="scenario">场景</option>
                    <option value="band">频段</option>
                    <option value="coverage_type">覆盖类型</option>
                    <option value="device_model">设备型号</option>
                `;
            } else {
                searchColumn.innerHTML += `
                    <option value="gnb_ci">gNB CI</option>
                    <option value="cell_name">小区名称</option>
                    <option value="site_id">基站ID</option>
                    <option value="district">地市</option>
                    <option value="scenario">场景</option>
                    <option value="frequency_band">频段</option>
                    <option value="device_vendor">设备厂商</option>
                    <option value="rru_model">RRU型号</option>
                `;
            }
        }

        // 搜索工参
        async function searchParams(page = 1) {
            const searchType = document.getElementById('searchType').value;
            const searchColumn = document.getElementById('searchColumn').value;
            const searchTerm = document.getElementById('searchTerm').value.trim();
            const pageSizeValue = document.getElementById('pageSize').value;

            // 处理"全部数据"选项
            let pageSize, showAll = false;
            if (pageSizeValue === 'all') {
                pageSize = 999999; // 设置一个很大的数字
                showAll = true;
                page = 1; // 全部数据时强制第一页
            } else {
                pageSize = parseInt(pageSizeValue);
            }

            const loadingDiv = document.getElementById('searchLoading');
            const tableDiv = document.getElementById('searchTable');

            // 显示加载状态
            loadingDiv.style.display = 'block';
            tableDiv.style.display = 'none';

            // 如果是全部数据，显示特殊的加载提示
            if (showAll) {
                loadingDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">正在加载全部数据，请耐心等待...</p>
                        <p class="text-muted">数据量较大时可能需要较长时间</p>
                    </div>
                `;
            }

            try {
                const response = await fetch('/api/network_params/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: searchType,
                        search_column: searchColumn,
                        search_term: searchTerm,
                        page: page,
                        page_size: pageSize,
                        show_all: showAll
                    })
                });

                const result = await response.json();

                if (result.success) {
                    currentSearchData = result.data;
                    currentPage = page;
                    renderSearchResults(result.data, searchType, showAll);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('搜索失败:', error);
                tableDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ 搜索失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                tableDiv.style.display = 'block';
            } finally {
                // 恢复默认加载提示
                loadingDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">正在搜索数据...</p>
                    </div>
                `;
                loadingDiv.style.display = 'none';
            }
        }

        // 渲染搜索结果
        function renderSearchResults(data, searchType, showAll = false) {
            const tableHead = document.getElementById('searchTableHead');
            const tableBody = document.getElementById('searchTableBody');
            const tableDiv = document.getElementById('searchTable');

            // 生成表头
            let headers = [];
            if (searchType === '4g') {
                headers = ['小区ID', '小区名称', '基站ID', '经度', '纬度', '频段', 'PCI', '方位角', '功率', '地市', '县区', '场景'];
            } else {
                headers = ['gNB CI', '小区名称', '基站ID', '经度', '纬度', '频段', 'PCI', '方位角', '功率', '地市', '场景'];
            }

            tableHead.innerHTML = `
                <tr>
                    ${headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            `;

            // 生成表格内容
            if (data.records.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="${headers.length}" style="text-align: center; padding: 40px; color: #6c757d;">
                            暂无数据
                        </td>
                    </tr>
                `;
            } else {
                tableBody.innerHTML = data.records.map(record => {
                    let cells = [];
                    if (searchType === '4g') {
                        cells = [
                            record.cell_id || '-',
                            record.cell_name || '-',
                            record.site_id || '-',
                            record.longitude ? record.longitude.toFixed(6) : '-',
                            record.latitude ? record.latitude.toFixed(6) : '-',
                            record.band || '-',
                            record.pci || '-',
                            record.azimuth || '-',
                            record.power || '-',
                            record.district || '-',
                            record.county || '-',
                            record.scenario || '-'
                        ];
                    } else {
                        cells = [
                            record.gnb_ci || '-',
                            record.cell_name || '-',
                            record.site_id || '-',
                            record.longitude ? record.longitude.toFixed(6) : '-',
                            record.latitude ? record.latitude.toFixed(6) : '-',
                            record.frequency_band || '-',
                            record.pci || '-',
                            record.azimuth || '-',
                            record.power || '-',
                            record.district || '-',
                            record.scenario || '-'
                        ];
                    }

                    return `<tr>${cells.map(cell => `<td>${cell}</td>`).join('')}</tr>`;
                }).join('');
            }

            // 生成分页（全量显示时不显示分页）
            if (!showAll) {
                renderPagination(data);
            } else {
                document.getElementById('searchPagination').innerHTML = `
                    <div class="alert alert-success">
                        <strong>📊 全量数据展示</strong> - 共显示 ${data.records.length} 条记录
                    </div>
                `;
            }

            tableDiv.style.display = 'block';
        }

        // 渲染分页
        function renderPagination(data) {
            const paginationDiv = document.getElementById('searchPagination');

            if (data.total_pages <= 1) {
                paginationDiv.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `
                <button onclick="searchParams(${data.page - 1})" ${data.page <= 1 ? 'disabled' : ''}>
                    ← 上一页
                </button>
            `;

            // 页码按钮
            const startPage = Math.max(1, data.page - 2);
            const endPage = Math.min(data.total_pages, data.page + 2);

            if (startPage > 1) {
                paginationHTML += `<button onclick="searchParams(1)">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span>...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button onclick="searchParams(${i})" ${i === data.page ? 'class="current-page"' : ''}>
                        ${i}
                    </button>
                `;
            }

            if (endPage < data.total_pages) {
                if (endPage < data.total_pages - 1) {
                    paginationHTML += `<span>...</span>`;
                }
                paginationHTML += `<button onclick="searchParams(${data.total_pages})">${data.total_pages}</button>`;
            }

            // 下一页按钮
            paginationHTML += `
                <button onclick="searchParams(${data.page + 1})" ${data.page >= data.total_pages ? 'disabled' : ''}>
                    下一页 →
                </button>
            `;

            // 显示统计信息
            paginationHTML += `
                <span style="margin-left: 20px; color: #6c757d;">
                    第 ${data.page} 页，共 ${data.total_pages} 页，总计 ${data.total_count.toLocaleString()} 条记录
                </span>
            `;

            paginationDiv.innerHTML = paginationHTML;
        }

        // 清空搜索
        function clearSearch() {
            document.getElementById('searchTerm').value = '';
            document.getElementById('searchColumn').value = 'all';
            document.getElementById('searchTable').style.display = 'none';
            currentSearchData = null;
            currentPage = 1;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSearchColumns();
        });

        // 加载导入历史
        async function loadHistory() {
            const loadingDiv = document.getElementById('historyLoading');
            const tableDiv = document.getElementById('historyTable');

            loadingDiv.style.display = 'block';
            tableDiv.style.display = 'none';

            try {
                const response = await fetch('/api/network_params/stats');
                const result = await response.json();

                if (result.success) {
                    renderHistory(result.data.import_history);
                    tableDiv.style.display = 'block';
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('加载历史记录失败:', error);
                tableDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ 加载失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                tableDiv.style.display = 'block';
            } finally {
                loadingDiv.style.display = 'none';
            }
        }

        // 渲染历史记录
        function renderHistory(history) {
            const tableBody = document.getElementById('historyTableBody');

            if (history.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            暂无导入记录
                        </td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = history.map(record => `
                <tr>
                    <td>
                        <span style="background: ${record.type === '4g' ? '#007bff' : '#28a745'}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.8rem;">
                            ${record.type.toUpperCase()}
                        </span>
                    </td>
                    <td>${record.filename}</td>
                    <td>${record.file_date || '-'}</td>
                    <td>${record.total_records.toLocaleString()}</td>
                    <td style="color: #28a745; font-weight: 600;">${record.success_records.toLocaleString()}</td>
                    <td style="color: ${record.error_records > 0 ? '#dc3545' : '#6c757d'}; font-weight: 600;">
                        ${record.error_records}
                    </td>
                    <td>${record.duration}s</td>
                    <td>${new Date(record.import_time).toLocaleString()}</td>
                </tr>
            `).join('');
        }

        // 监听搜索框回车事件
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchTerm').addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    searchParams();
                }
            });
        });
    </script>
</body>
</html>
