#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瞬断模块修复验证脚本
验证所有修复是否生效
"""

import os
import pandas as pd
import json
from datetime import datetime

def verify_default_file_removed():
    """验证默认文件是否已删除"""
    print("🗑️  验证默认文件删除状态...")
    print("-" * 40)
    
    default_file = '7月至8.4频繁瞬断.xlsx'
    backup_dir = 'backup_default_files'
    
    # 检查默认文件是否已删除
    if os.path.exists(default_file):
        print(f"❌ 默认文件仍然存在: {default_file}")
        return False
    else:
        print(f"✅ 默认文件已删除: {default_file}")
    
    # 检查备份是否存在
    if os.path.exists(backup_dir):
        backup_files = [f for f in os.listdir(backup_dir) if f.startswith('7月至8.4频繁瞬断')]
        if backup_files:
            print(f"✅ 备份文件已创建: {len(backup_files)} 个")
        else:
            print(f"⚠️  备份目录存在但无备份文件")
    else:
        print(f"⚠️  备份目录不存在")
    
    return True

def verify_file_selection():
    """验证文件选择逻辑"""
    print("\n🎯 验证文件选择逻辑...")
    print("-" * 40)
    
    current_dir = os.getcwd()
    excel_files = []
    default_file = '7月至8.4频繁瞬断.xlsx'
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            file_path = os.path.join(current_dir, file)
            file_time = os.path.getmtime(file_path)
            
            # 使用修复后的优先级逻辑
            if '瞬断告警' in file:
                priority = 4  # 原始瞬断告警数据最高优先级
            elif '频繁瞬断' in file and file != default_file:
                priority = 3  # 用户上传的瞬断文件高优先级
            elif '频繁瞬断' in file and file == default_file:
                priority = 1  # 默认文件较低优先级
            elif '瞬断' in file:
                priority = 2  # 其他瞬断文件中等优先级
            else:
                priority = 0  # 其他文件最低优先级
                
            excel_files.append((file, file_time, priority))
    
    if excel_files:
        excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
        selected_file = excel_files[0][0]
        
        print(f"✅ 系统将选择: {selected_file}")
        print(f"   优先级: {excel_files[0][2]}")
        
        if '瞬断告警' in selected_file:
            print(f"✅ 正确选择原始告警数据文件")
            return True, selected_file
        else:
            print(f"⚠️  未选择原始告警数据文件")
            return False, selected_file
    else:
        print(f"❌ 未找到任何Excel文件")
        return False, None

def verify_column_mapping(filename):
    """验证列映射功能"""
    print(f"\n🔍 验证列映射功能: {filename}")
    print("-" * 40)
    
    try:
        df = pd.read_excel(filename)
        
        # 模拟列映射检测
        column_mapping = {}
        for col in df.columns:
            col_str = str(col).strip()
            
            if '网元名称' in col_str:
                column_mapping['网元名称'] = col
            elif '告警' in col_str and '发生' in col_str:
                column_mapping['告警产生时间'] = col
            elif '设备类型' in col_str:
                column_mapping['网管设备类型'] = col
            elif '室分' in col_str:
                column_mapping['是否为室分系统信号源'] = col
        
        required_fields = ['网元名称', '告警产生时间']
        missing_fields = [f for f in required_fields if f not in column_mapping]
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {missing_fields}")
            return False
        else:
            print(f"✅ 列映射检测成功")
            print(f"   映射字段: {list(column_mapping.keys())}")
            return True
            
    except Exception as e:
        print(f"❌ 列映射验证失败: {str(e)}")
        return False

def verify_data_processing(filename):
    """验证数据处理功能"""
    print(f"\n⚙️  验证数据处理功能: {filename}")
    print("-" * 40)
    
    try:
        df = pd.read_excel(filename)
        
        # 重命名列（简化版）
        if '告警发生时间' in df.columns:
            df['告警产生时间'] = df['告警发生时间']
        
        # 时间处理
        if '告警产生时间' in df.columns:
            df['告警产生时间'] = pd.to_datetime(df['告警产生时间'], errors='coerce')
            valid_times = df['告警产生时间'].notna().sum()
            print(f"✅ 时间处理成功: {valid_times}/{len(df)} 有效记录")
        
        # 网元统计
        if '网元名称' in df.columns:
            unique_cells = df['网元名称'].nunique()
            print(f"✅ 网元统计成功: {unique_cells} 个唯一网元")
        
        # 简单分组统计
        if '网元名称' in df.columns:
            grouped = df.groupby('网元名称').size()
            top_5 = grouped.nlargest(5)
            print(f"✅ 分组统计成功，瞬断最多的5个网元:")
            for i, (name, count) in enumerate(top_5.items(), 1):
                print(f"   {i}. {name[:30]}... : {count}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理验证失败: {str(e)}")
        return False

def verify_web_integration():
    """验证Web集成状态"""
    print(f"\n🌐 验证Web集成状态...")
    print("-" * 40)
    
    # 检查模板文件
    template_file = 'templates/frequent_outage_analyzer.html'
    if os.path.exists(template_file):
        print(f"✅ 瞬断分析页面模板存在")
    else:
        print(f"❌ 瞬断分析页面模板缺失")
        return False
    
    # 检查web_app.py中的相关路由
    try:
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '/frequent_outage_analyzer' in content:
            print(f"✅ 瞬断分析路由存在")
        else:
            print(f"❌ 瞬断分析路由缺失")
            return False
            
        if 'api/frequent_outage/analyze' in content:
            print(f"✅ 瞬断分析API存在")
        else:
            print(f"❌ 瞬断分析API缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Web集成验证失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🔧 瞬断模块修复验证")
    print("=" * 60)
    print("验证所有修复是否正确生效...")
    print("=" * 60)
    
    results = {}
    
    # 验证1: 默认文件删除
    results['default_removed'] = verify_default_file_removed()
    
    # 验证2: 文件选择逻辑
    file_selection_ok, selected_file = verify_file_selection()
    results['file_selection'] = file_selection_ok
    
    # 验证3: 列映射功能
    if selected_file:
        results['column_mapping'] = verify_column_mapping(selected_file)
        
        # 验证4: 数据处理功能
        results['data_processing'] = verify_data_processing(selected_file)
    else:
        results['column_mapping'] = False
        results['data_processing'] = False
    
    # 验证5: Web集成
    results['web_integration'] = verify_web_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 验证结果总结:")
    print("=" * 60)
    
    test_names = {
        'default_removed': '默认文件删除',
        'file_selection': '文件选择逻辑',
        'column_mapping': '列映射功能',
        'data_processing': '数据处理功能',
        'web_integration': 'Web集成状态'
    }
    
    passed = 0
    total = len(results)
    
    for key, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_names[key]}")
        if result:
            passed += 1
    
    print(f"\n📊 验证统计: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 所有验证通过!")
        print("✅ 瞬断模块修复完全成功")
        print("\n💡 使用说明:")
        print("1. 启动Web应用: python web_app.py")
        print("2. 访问瞬断分析页面")
        print("3. 系统会自动使用你的原始瞬断告警数据")
        print("4. 上传新文件时，确保文件名包含'瞬断告警'以获得最高优先级")
        
    else:
        print(f"\n⚠️  {total - passed} 项验证失败")
        print("💡 建议:")
        print("1. 检查失败的验证项")
        print("2. 重新运行修复脚本")
        print("3. 确保所有文件都在正确位置")

if __name__ == "__main__":
    main()
