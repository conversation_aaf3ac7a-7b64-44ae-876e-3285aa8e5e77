#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件选择逻辑
"""

import os
from datetime import datetime

def find_latest_outage_file():
    """查找最新的频繁瞬断文件和对应的列映射"""
    current_dir = os.getcwd()

    # 查找所有Excel文件，优先使用用户上传的文件
    excel_files = []
    default_file = '7月至8.4频繁瞬断.xlsx'
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            file_path = os.path.join(current_dir, file)
            file_time = os.path.getmtime(file_path)
            
            # 设置优先级：原始瞬断告警数据 > 用户上传的瞬断文件 > 其他瞬断文件 > 默认文件 > 其他文件
            if '瞬断告警' in file:
                priority = 4  # 原始瞬断告警数据最高优先级
            elif '频繁瞬断' in file and file != default_file:
                priority = 3  # 用户上传的瞬断文件高优先级
            elif '频繁瞬断' in file and file == default_file:
                priority = 1  # 默认文件较低优先级
            elif '瞬断' in file:
                priority = 2  # 其他瞬断文件中等优先级
            else:
                priority = 0  # 其他文件最低优先级
                
            excel_files.append((file, file_time, priority))

    # 如果没有找到任何Excel文件
    if not excel_files:
        raise FileNotFoundError("没有找到频繁瞬断数据文件")

    # 按优先级和时间排序，获取最新文件
    excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
    latest_file = excel_files[0][0]
    
    print(f"选择的瞬断数据文件: {latest_file} (优先级: {excel_files[0][2]})")

    # 查找对应的列映射文件
    mapping_file = latest_file.rsplit('.', 1)[0] + '_mapping.json'
    column_mapping = None

    if os.path.exists(mapping_file):
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                import json
                column_mapping = json.load(f)
        except:
            column_mapping = None

    return latest_file, column_mapping

def main():
    """主测试函数"""
    print("🔍 测试文件选择逻辑")
    print("=" * 50)
    
    try:
        # 显示所有瞬断相关文件
        current_dir = os.getcwd()
        outage_files = []
        
        for file in os.listdir(current_dir):
            if file.endswith(('.xlsx', '.xls')) and '瞬断' in file:
                file_path = os.path.join(current_dir, file)
                file_time = os.path.getmtime(file_path)
                file_size = os.path.getsize(file_path)
                
                # 计算优先级
                if '瞬断告警' in file:
                    priority = 4
                    priority_name = "原始告警数据"
                elif '频繁瞬断' in file:
                    priority = 3
                    priority_name = "分析结果文件"
                else:
                    priority = 2
                    priority_name = "其他瞬断文件"
                
                outage_files.append({
                    'name': file,
                    'size': file_size,
                    'modified': datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S'),
                    'priority': priority,
                    'priority_name': priority_name
                })
        
        # 按优先级排序
        outage_files.sort(key=lambda x: (x['priority'], x['modified']), reverse=True)
        
        print(f"📋 找到 {len(outage_files)} 个瞬断相关文件:")
        for i, file_info in enumerate(outage_files, 1):
            status = "🎯 [将被选择]" if i == 1 else ""
            print(f"   {i}. {file_info['name']} {status}")
            print(f"      优先级: {file_info['priority']} ({file_info['priority_name']})")
            print(f"      大小: {file_info['size']:,} 字节")
            print(f"      修改时间: {file_info['modified']}")
            print()
        
        # 测试文件选择函数
        print("🎯 文件选择结果:")
        selected_file, mapping = find_latest_outage_file()
        
        print(f"✅ 选择的文件: {selected_file}")
        if mapping:
            print(f"✅ 找到列映射文件")
            print(f"📊 映射内容: {mapping}")
        else:
            print(f"ℹ️  未找到列映射文件")
        
        # 验证选择的文件是否是原始告警数据
        if '瞬断告警' in selected_file:
            print(f"✅ 正确选择了原始瞬断告警数据文件")
            print(f"💡 这个文件包含完整的告警记录，适合进行瞬断分析")
        else:
            print(f"⚠️  选择的不是原始告警数据文件")
            print(f"💡 建议使用包含'瞬断告警'的原始数据文件")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
