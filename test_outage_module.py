#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断模块是否正确使用用户导入的数据
"""

import os
import pandas as pd
from datetime import datetime

def test_file_selection():
    """测试文件选择逻辑"""
    print("🔍 测试瞬断文件选择逻辑...")
    print("=" * 50)
    
    try:
        # 模拟 find_latest_outage_file 函数的逻辑
        current_dir = os.getcwd()
        excel_files = []
        default_file = '7月至8.4频繁瞬断.xlsx'
        
        for file in os.listdir(current_dir):
            if file.endswith(('.xlsx', '.xls')):
                file_path = os.path.join(current_dir, file)
                file_time = os.path.getmtime(file_path)
                
                # 设置优先级：用户上传的瞬断文件 > 其他瞬断文件 > 默认文件 > 其他文件
                if '频繁瞬断' in file and file != default_file:
                    priority = 3  # 用户上传的瞬断文件最高优先级
                elif '频繁瞬断' in file and file == default_file:
                    priority = 1  # 默认文件较低优先级
                elif '瞬断' in file:
                    priority = 2  # 其他瞬断文件中等优先级
                else:
                    priority = 0  # 其他文件最低优先级
                    
                excel_files.append((file, file_time, priority))
        
        if not excel_files:
            print("❌ 未找到任何Excel文件")
            return None
        
        # 按优先级和时间排序，获取最新文件
        excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
        selected_file = excel_files[0][0]
        
        print(f"✅ 选择的文件: {selected_file}")
        print(f"   优先级: {excel_files[0][2]}")
        print(f"   修改时间: {datetime.fromtimestamp(excel_files[0][1]).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 确认不是默认文件
        if selected_file == default_file:
            print("❌ 警告: 仍在使用默认文件!")
            return None
        else:
            print("✅ 确认: 正在使用用户上传的文件")
            return selected_file
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return None

def test_file_content(filename):
    """测试文件内容"""
    print(f"\n📄 测试文件内容: {filename}")
    print("=" * 50)
    
    try:
        # 读取文件
        df = pd.read_excel(filename)
        
        print(f"✅ 文件读取成功")
        print(f"   行数: {len(df):,}")
        print(f"   列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n📋 文件列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        
        # 检查关键列
        key_columns = ['告警产生时间', '网元名称', '日期']
        found_columns = []
        missing_columns = []
        
        for col in key_columns:
            if col in df.columns:
                found_columns.append(col)
            else:
                missing_columns.append(col)
        
        print(f"\n🔍 关键列检查:")
        if found_columns:
            print(f"   ✅ 找到列: {', '.join(found_columns)}")
        if missing_columns:
            print(f"   ⚠️  缺失列: {', '.join(missing_columns)}")
        
        # 显示前几行数据
        print(f"\n📊 数据预览 (前3行):")
        print(df.head(3).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 文件内容测试失败: {str(e)}")
        return False

def test_data_processing(filename):
    """测试数据处理逻辑"""
    print(f"\n⚙️  测试数据处理逻辑: {filename}")
    print("=" * 50)
    
    try:
        df = pd.read_excel(filename)
        
        # 数据预处理（模拟web_app.py中的逻辑）
        if '告警产生时间' in df.columns:
            df['告警产生时间'] = pd.to_datetime(df['告警产生时间'], errors='coerce')
            df['日期'] = df['告警产生时间'].dt.date
            print("✅ 时间列处理成功")
        
        # 统计基本信息
        if '网元名称' in df.columns:
            unique_cells = df['网元名称'].nunique()
            print(f"✅ 唯一网元数量: {unique_cells:,}")
        
        if '日期' in df.columns:
            date_range = df['日期'].dropna()
            if not date_range.empty:
                min_date = date_range.min()
                max_date = date_range.max()
                print(f"✅ 数据时间范围: {min_date} 至 {max_date}")
        
        # 模拟分组统计
        if '网元名称' in df.columns:
            grouped_data = {}
            for _, row in df.iterrows():
                network_name = str(row.get('网元名称', ''))
                if network_name and network_name != 'nan':
                    if network_name not in grouped_data:
                        grouped_data[network_name] = {
                            '瞬断次数': 0,
                            '瞬断详情': []
                        }
                    grouped_data[network_name]['瞬断次数'] += 1
            
            print(f"✅ 处理后网元数量: {len(grouped_data):,}")
            
            # 显示前5个网元的统计
            if grouped_data:
                print(f"\n📊 前5个网元瞬断统计:")
                sorted_items = sorted(grouped_data.items(), key=lambda x: x[1]['瞬断次数'], reverse=True)
                for i, (name, data) in enumerate(sorted_items[:5], 1):
                    print(f"   {i}. {name}: {data['瞬断次数']}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 瞬断模块数据使用测试")
    print("=" * 60)
    print("目标: 验证系统是否正确使用用户导入的瞬断数据")
    print("=" * 60)
    
    # 测试1: 文件选择
    selected_file = test_file_selection()
    
    if not selected_file:
        print("\n❌ 文件选择测试失败，无法继续")
        return
    
    # 测试2: 文件内容
    content_ok = test_file_content(selected_file)
    
    if not content_ok:
        print("\n❌ 文件内容测试失败，无法继续")
        return
    
    # 测试3: 数据处理
    processing_ok = test_data_processing(selected_file)
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print("=" * 60)
    
    if selected_file and content_ok and processing_ok:
        print("✅ 所有测试通过!")
        print(f"✅ 系统将正确使用文件: {selected_file}")
        print("✅ 文件内容格式正确")
        print("✅ 数据处理逻辑正常")
        
        print("\n💡 下一步建议:")
        print("1. 启动Web应用: python web_app.py")
        print("2. 访问瞬断分析页面")
        print("3. 进行实际的瞬断分析测试")
        print("4. 检查控制台日志确认使用的文件")
        
    else:
        print("❌ 部分测试失败")
        print("💡 建议:")
        print("1. 检查瞬断数据文件格式")
        print("2. 确保文件包含必要的列")
        print("3. 重新上传正确格式的瞬断数据")

if __name__ == "__main__":
    main()
