#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列映射检测功能
"""

import pandas as pd

def detect_column_mapping(columns):
    """智能检测列名映射"""
    column_mapping = {}

    # 直接检查是否有必要的列名
    for col in columns:
        col_str = str(col).strip()

        # 网元名称 - 包含匹配
        if '网元名称' in col_str or col_str in ['小区名称', '基站名称', '站点名称']:
            if '网元名称' not in column_mapping:
                column_mapping['网元名称'] = col

        # 告警时间 - 支持发生、发现、产生时间
        elif (('告警' in col_str and ('发生' in col_str or '发现' in col_str or '产生' in col_str)) or
              col_str in ['故障发生时间', '开始时间', '告警发生时间', '告警发现时间', '告警产生时间']):
            if '告警产生时间' not in column_mapping:
                column_mapping['告警产生时间'] = col

        # 告警清除时间 - 包含匹配
        elif ('告警' in col_str and '清除' in col_str) or ('告警' in col_str and '清除' in col_str) or col_str in ['告警消失时间', '结束时间', '清除时间']:
            if '告警清除时间' not in column_mapping:
                column_mapping['告警清除时间'] = col

        # 室分信息 - 包含匹配
        elif '室分' in col_str or col_str in ['是否为室分系统信号源', '室分标识', '覆盖类型']:
            if '是否为室分系统信号源' not in column_mapping:
                column_mapping['是否为室分系统信号源'] = col

        # 网络制式 - 包含匹配
        elif '设备类型' in col_str or col_str in ['网络制式', '制式']:
            if '网管设备类型' not in column_mapping:
                column_mapping['网管设备类型'] = col

        # 日期
        elif col_str in ['日期', '故障日期', '发生日期']:
            if '日期' not in column_mapping:
                column_mapping['日期'] = col

    # 检查必要字段
    required_fields = ['网元名称', '告警产生时间']
    for field in required_fields:
        if field not in column_mapping:
            # 如果没有找到，返回None表示检测失败
            print(f"缺少必要字段: {field}")
            print(f"可用列名: {[str(col) for col in columns]}")
            return None

    return column_mapping

def test_file_mapping(filename):
    """测试文件的列映射"""
    print(f"🔍 测试文件: {filename}")
    print("=" * 50)
    
    try:
        df = pd.read_excel(filename)
        print(f"✅ 文件读取成功，行数: {len(df)}")
        
        print(f"\n📋 文件列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n🔍 列映射检测:")
        column_mapping = detect_column_mapping(df.columns)
        
        if column_mapping:
            print(f"✅ 列映射检测成功!")
            print(f"📊 映射结果:")
            for key, value in column_mapping.items():
                print(f"   {key} -> {value}")
            
            # 测试数据处理
            print(f"\n⚙️  测试数据处理:")
            df_renamed = df.rename(columns={v: k for k, v in column_mapping.items()})
            
            if '告警产生时间' in df_renamed.columns:
                df_renamed['告警产生时间'] = pd.to_datetime(df_renamed['告警产生时间'], errors='coerce')
                valid_times = df_renamed['告警产生时间'].notna().sum()
                print(f"   ✅ 有效时间记录: {valid_times}/{len(df_renamed)}")
                
                if valid_times > 0:
                    min_time = df_renamed['告警产生时间'].min()
                    max_time = df_renamed['告警产生时间'].max()
                    print(f"   📅 时间范围: {min_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {max_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if '网元名称' in df_renamed.columns:
                unique_cells = df_renamed['网元名称'].nunique()
                print(f"   📡 唯一网元数量: {unique_cells}")
            
            return True
        else:
            print(f"❌ 列映射检测失败!")
            return False
            
    except Exception as e:
        print(f"❌ 文件处理失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 瞬断文件列映射检测测试")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        '8月瞬断告警.xlsx',
        '频繁瞬断分析_包含最近日期.xlsx'
    ]
    
    results = {}
    
    for filename in test_files:
        try:
            results[filename] = test_file_mapping(filename)
            print()
        except Exception as e:
            print(f"❌ 测试 {filename} 失败: {str(e)}")
            results[filename] = False
            print()
    
    # 总结
    print("=" * 60)
    print("🎯 测试结果总结:")
    print("=" * 60)
    
    for filename, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {filename}")
    
    successful_files = [f for f, r in results.items() if r]
    
    if successful_files:
        print(f"\n💡 建议使用文件: {successful_files[0]}")
        print("这些文件的列映射检测成功，可以正常处理")
    else:
        print(f"\n⚠️  所有文件的列映射检测都失败")
        print("请检查文件格式是否正确")

if __name__ == "__main__":
    main()
