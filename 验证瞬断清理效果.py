#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证瞬断模块清理效果
确认系统不再使用默认数据，只能通过上传使用
"""

import os
import requests
import time

def check_file_status():
    """检查文件状态"""
    print("📁 检查瞬断文件状态...")
    print("=" * 50)
    
    current_dir = os.getcwd()
    outage_files = []
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            if any(keyword in file for keyword in ['瞬断', '频繁瞬断']):
                outage_files.append(file)
    
    if outage_files:
        print(f"⚠️  仍有瞬断文件存在:")
        for file in outage_files:
            print(f"   - {file}")
        return False
    else:
        print(f"✅ 所有默认瞬断文件已清理")
        print(f"✅ 系统现在不会自动选择任何瞬断文件")
        return True

def check_backup_status():
    """检查备份状态"""
    print(f"\n📦 检查备份状态...")
    print("=" * 50)
    
    backup_dir = 'backup_outage_default_data'
    
    if os.path.exists(backup_dir):
        backup_files = os.listdir(backup_dir)
        print(f"✅ 备份目录存在: {backup_dir}")
        print(f"📊 备份文件数量: {len(backup_files)}")
        
        if backup_files:
            print(f"📋 备份文件列表:")
            for file in backup_files[:5]:  # 只显示前5个
                print(f"   - {file}")
            if len(backup_files) > 5:
                print(f"   ... 还有{len(backup_files) - 5}个文件")
        
        return True
    else:
        print(f"❌ 备份目录不存在")
        return False

def test_api_without_data():
    """测试没有数据时的API响应"""
    print(f"\n🌐 测试API响应 (无数据状态)...")
    print("=" * 50)
    
    try:
        # 检查服务器状态
        try:
            response = requests.get('http://localhost:5000', timeout=2)
            print("✅ Web服务器正在运行")
        except:
            print("❌ Web服务器未运行")
            print("💡 请先启动: python web_app.py")
            return False
        
        # 测试时间范围API
        print(f"\n🔍 测试时间范围API...")
        response = requests.get('http://localhost:5000/api/get_outage_date_range', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"⚠️  API仍返回成功，可能有残留数据")
                print(f"   数据源: {data.get('source', 'N/A')}")
                print(f"   文件名: {data.get('filename', 'N/A')}")
                return False
            else:
                print(f"✅ API正确返回失败状态")
                print(f"   错误信息: {data.get('error', 'N/A')}")
                return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
        
        # 测试分析API
        print(f"\n📊 测试分析API...")
        test_data = {
            "start_date": "2025-08-01",
            "end_date": "2025-08-13"
        }
        
        response = requests.post('http://localhost:5000/api/frequent_outage/analyze', 
                               json=test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"⚠️  分析API仍返回成功，可能有残留数据")
                return False
            else:
                print(f"✅ 分析API正确返回失败状态")
                print(f"   错误信息: {result.get('error', 'N/A')}")
                print(f"   提示信息: {result.get('message', 'N/A')}")
                return True
        else:
            print(f"❌ 分析API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_page_behavior():
    """测试页面行为"""
    print(f"\n🌐 测试页面行为...")
    print("=" * 50)
    
    try:
        response = requests.get('http://localhost:5000/frequent_outage_analyzer', timeout=10)
        
        if response.status_code == 200:
            print("✅ 瞬断分析页面访问正常")
            
            content = response.text
            
            # 检查页面是否包含上传功能
            upload_features = [
                ('上传区域', '上传瞬断数据文件' in content),
                ('文件输入', 'type="file"' in content),
                ('使用说明', '使用说明' in content),
                ('当前数据源', '当前数据源' in content)
            ]
            
            print(f"📋 页面功能检查:")
            for feature_name, exists in upload_features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            return all(exists for _, exists in upload_features)
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面测试失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🔍 验证瞬断模块清理效果")
    print("=" * 60)
    print("验证系统是否已完全清理默认瞬断数据...")
    print("=" * 60)
    
    # 检查文件状态
    files_clean = check_file_status()
    
    # 检查备份状态
    backup_ok = check_backup_status()
    
    # 检查服务器状态
    print(f"\n🌐 检查Web服务器状态...")
    try:
        response = requests.get('http://localhost:5000', timeout=2)
        server_running = True
        print("✅ Web服务器正在运行")
    except:
        server_running = False
        print("❌ Web服务器未运行")
        print("💡 请先启动: python web_app.py")
    
    # 测试API响应
    if server_running:
        api_clean = test_api_without_data()
        page_ok = test_page_behavior()
    else:
        api_clean = None
        page_ok = None
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 清理效果验证结果:")
    print("=" * 60)
    
    print(f"📁 文件清理: {'✅ 完成' if files_clean else '❌ 未完成'}")
    print(f"📦 数据备份: {'✅ 正常' if backup_ok else '❌ 异常'}")
    
    if server_running:
        print(f"🌐 API响应: {'✅ 正确' if api_clean else '❌ 异常'}")
        print(f"📄 页面功能: {'✅ 正常' if page_ok else '❌ 异常'}")
    else:
        print(f"🌐 API响应: ⏭️  跳过 (服务器未运行)")
        print(f"📄 页面功能: ⏭️  跳过 (服务器未运行)")
    
    # 综合评估
    if files_clean and backup_ok:
        if not server_running:
            print(f"\n🎉 瞬断模块清理成功!")
            print(f"✅ 所有默认瞬断数据已清理")
            print(f"✅ 数据已安全备份")
            print(f"💡 请启动服务器进行完整测试")
        elif api_clean and page_ok:
            print(f"\n🎉 瞬断模块清理完全成功!")
            print(f"✅ 所有默认瞬断数据已清理")
            print(f"✅ API正确响应无数据状态")
            print(f"✅ 页面功能完整")
            print(f"✅ 系统只能通过上传使用瞬断数据")
        else:
            print(f"\n⚠️  清理基本完成，但需要检查API或页面")
    else:
        print(f"\n❌ 清理未完全成功")
        print(f"💡 建议重新运行清理脚本")
    
    print(f"\n💡 现在瞬断模块的使用方式:")
    print(f"1. 📤 必须在页面上传瞬断告警文件")
    print(f"2. 🚫 系统不会自动选择任何默认文件")
    print(f"3. 🎯 只使用用户导入的实际数据")
    print(f"4. 📊 获得基于真实数据的分析结果")

if __name__ == "__main__":
    main()
