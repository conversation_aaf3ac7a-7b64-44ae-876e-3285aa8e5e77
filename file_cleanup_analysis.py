#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件清理分析脚本
"""

import os
import glob
import time
from datetime import datetime

def analyze_temp_files():
    """分析临时文件"""
    print("分析临时文件...")
    
    # 检查当前目录的临时文件
    temp_patterns = [
        'temp_*.png',
        'temp_*.jpg', 
        'temp_*.jpeg',
        'temp_*.html',
        'temp_*.pdf'
    ]
    
    temp_files = []
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        temp_files.extend(files)
    
    print(f"当前目录临时文件: {len(temp_files)} 个")
    for file in temp_files:
        try:
            size = os.path.getsize(file)
            mtime = os.path.getmtime(file)
            mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            print(f"  {file} - {size} 字节 - {mtime_str}")
        except Exception as e:
            print(f"  {file} - 无法获取信息: {str(e)}")
    
    return temp_files

def analyze_uploads_directory():
    """分析uploads目录"""
    print("\n分析uploads目录...")
    
    uploads_dir = 'uploads'
    if not os.path.exists(uploads_dir):
        print("uploads目录不存在")
        return []
    
    files = []
    total_size = 0
    
    for root, dirs, filenames in os.walk(uploads_dir):
        for filename in filenames:
            filepath = os.path.join(root, filename)
            try:
                size = os.path.getsize(filepath)
                total_size += size
                mtime = os.path.getmtime(filepath)
                files.append({
                    'path': filepath,
                    'size': size,
                    'mtime': mtime,
                    'mtime_str': datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            except Exception as e:
                print(f"无法获取文件信息: {filepath} - {str(e)}")
    
    print(f"uploads目录文件: {len(files)} 个")
    print(f"总大小: {total_size / 1024 / 1024:.2f} MB")
    
    # 按时间排序，显示最新的10个文件
    files.sort(key=lambda x: x['mtime'], reverse=True)
    print("\n最新的10个文件:")
    for file in files[:10]:
        size_mb = file['size'] / 1024 / 1024
        print(f"  {file['path']} - {size_mb:.2f} MB - {file['mtime_str']}")
    
    return files

def analyze_exports_directory():
    """分析exports目录"""
    print("\n分析exports目录...")
    
    exports_dir = 'exports'
    if not os.path.exists(exports_dir):
        print("exports目录不存在")
        return []
    
    files = []
    total_size = 0
    
    for root, dirs, filenames in os.walk(exports_dir):
        for filename in filenames:
            filepath = os.path.join(root, filename)
            try:
                size = os.path.getsize(filepath)
                total_size += size
                mtime = os.path.getmtime(filepath)
                files.append({
                    'path': filepath,
                    'size': size,
                    'mtime': mtime,
                    'mtime_str': datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            except Exception as e:
                print(f"无法获取文件信息: {filepath} - {str(e)}")
    
    print(f"exports目录文件: {len(files)} 个")
    print(f"总大小: {total_size / 1024 / 1024:.2f} MB")
    
    # 按时间排序，显示最新的5个文件
    files.sort(key=lambda x: x['mtime'], reverse=True)
    print("\n最新的5个文件:")
    for file in files[:5]:
        size_mb = file['size'] / 1024 / 1024
        print(f"  {file['path']} - {size_mb:.2f} MB - {file['mtime_str']}")
    
    return files

def check_cleanup_mechanisms():
    """检查清理机制"""
    print("\n检查清理机制...")
    
    # 检查web_app.py中的清理代码
    cleanup_patterns = [
        'os.remove',
        'temp_files',
        '清理',
        'cleanup'
    ]
    
    try:
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        cleanup_count = 0
        for pattern in cleanup_patterns:
            count = content.count(pattern)
            cleanup_count += count
            print(f"  '{pattern}' 出现次数: {count}")
        
        print(f"总清理相关代码: {cleanup_count} 处")
        
        # 检查是否有定时清理机制
        if 'schedule' in content or 'cron' in content or 'timer' in content:
            print("  发现定时清理机制")
        else:
            print("  未发现定时清理机制")
            
    except Exception as e:
        print(f"检查清理机制失败: {str(e)}")

def create_cleanup_recommendations():
    """创建清理建议"""
    print("\n清理建议:")
    
    recommendations = [
        "1. 临时文件清理:",
        "   - 系统会在处理完成后自动清理temp_开头的文件",
        "   - 但某些异常情况下可能残留，建议定期手动清理",
        "",
        "2. uploads目录管理:",
        "   - 包含用户上传的原始文件和生成的报表",
        "   - 建议定期清理超过30天的文件",
        "   - 重要文件应该备份后再清理",
        "",
        "3. exports目录管理:",
        "   - 包含导出的图表和报告文件",
        "   - 这些文件通常供用户下载使用",
        "   - 建议保留最近的文件，清理过期文件",
        "",
        "4. 自动清理机制:",
        "   - 系统已实现临时文件的自动清理",
        "   - 建议添加定时清理任务",
        "   - 可以设置文件保留期限"
    ]
    
    for rec in recommendations:
        print(rec)

def clean_old_temp_files():
    """清理旧的临时文件"""
    print("\n清理旧的临时文件...")
    
    temp_patterns = [
        'temp_*.png',
        'temp_*.jpg', 
        'temp_*.jpeg',
        'temp_*.html',
        'temp_*.pdf'
    ]
    
    cleaned_count = 0
    cleaned_size = 0
    current_time = time.time()
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                # 检查文件年龄（超过1小时的临时文件）
                file_age = current_time - os.path.getmtime(file)
                if file_age > 3600:  # 1小时 = 3600秒
                    size = os.path.getsize(file)
                    os.remove(file)
                    cleaned_count += 1
                    cleaned_size += size
                    print(f"  已清理: {file} ({size} 字节)")
            except Exception as e:
                print(f"  清理失败: {file} - {str(e)}")
    
    if cleaned_count > 0:
        print(f"\n清理完成: {cleaned_count} 个文件, {cleaned_size / 1024:.2f} KB")
    else:
        print("\n没有需要清理的临时文件")

def main():
    """主函数"""
    print("文件清理分析报告")
    print("=" * 50)
    
    # 分析临时文件
    temp_files = analyze_temp_files()
    
    # 分析uploads目录
    uploads_files = analyze_uploads_directory()
    
    # 分析exports目录
    exports_files = analyze_exports_directory()
    
    # 检查清理机制
    check_cleanup_mechanisms()
    
    # 创建清理建议
    create_cleanup_recommendations()
    
    # 清理旧的临时文件
    clean_old_temp_files()
    
    print("\n" + "=" * 50)
    print("分析完成")

if __name__ == "__main__":
    main()
