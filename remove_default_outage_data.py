#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除默认瞬断数据文件脚本
确保系统优先使用用户导入的瞬断数据
"""

import os
import shutil
from datetime import datetime

def remove_default_outage_files():
    """删除默认的瞬断数据文件"""
    
    # 默认文件列表
    default_files = [
        '7月至8.4频繁瞬断.xlsx',
        '7月至8.4频繁瞬断_mapping.json'  # 可能存在的映射文件
    ]
    
    # 备份目录
    backup_dir = 'backup_default_files'
    
    print("🗑️  开始清理默认瞬断数据文件...")
    print("=" * 50)
    
    # 创建备份目录
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"✅ 创建备份目录: {backup_dir}")
    
    removed_files = []
    backed_up_files = []
    
    for file_name in default_files:
        if os.path.exists(file_name):
            try:
                # 备份文件
                backup_path = os.path.join(backup_dir, f"{file_name}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                shutil.copy2(file_name, backup_path)
                backed_up_files.append((file_name, backup_path))
                print(f"📦 备份文件: {file_name} -> {backup_path}")
                
                # 删除原文件
                os.remove(file_name)
                removed_files.append(file_name)
                print(f"🗑️  删除文件: {file_name}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_name} 时出错: {str(e)}")
        else:
            print(f"ℹ️  文件不存在: {file_name}")
    
    print("\n" + "=" * 50)
    print("📊 清理结果汇总:")
    print(f"✅ 成功删除 {len(removed_files)} 个默认文件")
    print(f"📦 成功备份 {len(backed_up_files)} 个文件")
    
    if removed_files:
        print("\n🗑️  已删除的文件:")
        for file_name in removed_files:
            print(f"   - {file_name}")
    
    if backed_up_files:
        print("\n📦 备份文件位置:")
        for original, backup in backed_up_files:
            print(f"   - {original} -> {backup}")
    
    return len(removed_files) > 0

def check_user_outage_files():
    """检查用户上传的瞬断文件"""
    print("\n🔍 检查用户上传的瞬断文件...")
    print("=" * 50)
    
    current_dir = os.getcwd()
    user_outage_files = []
    
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            # 排除默认文件，查找用户上传的瞬断文件
            if ('瞬断' in file or '频繁瞬断' in file) and file != '7月至8.4频繁瞬断.xlsx':
                file_path = os.path.join(current_dir, file)
                file_time = os.path.getmtime(file_path)
                file_size = os.path.getsize(file_path)
                
                user_outage_files.append({
                    'name': file,
                    'size': file_size,
                    'modified': datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')
                })
    
    if user_outage_files:
        print(f"✅ 找到 {len(user_outage_files)} 个用户瞬断文件:")
        for file_info in user_outage_files:
            print(f"   📄 {file_info['name']}")
            print(f"      大小: {file_info['size']:,} 字节")
            print(f"      修改时间: {file_info['modified']}")
            print()
        
        # 按修改时间排序，显示最新的文件
        user_outage_files.sort(key=lambda x: x['modified'], reverse=True)
        latest_file = user_outage_files[0]
        print(f"🎯 系统将优先使用最新文件: {latest_file['name']}")
        
    else:
        print("⚠️  未找到用户上传的瞬断文件")
        print("💡 建议:")
        print("   1. 上传包含'瞬断'关键词的Excel文件")
        print("   2. 确保文件格式正确（.xlsx 或 .xls）")
        print("   3. 检查文件是否包含必要的数据列")
    
    return len(user_outage_files) > 0

def verify_system_priority():
    """验证系统优先级设置"""
    print("\n🔧 验证系统文件优先级设置...")
    print("=" * 50)
    
    try:
        # 模拟系统的文件查找逻辑
        current_dir = os.getcwd()
        excel_files = []
        default_file = '7月至8.4频繁瞬断.xlsx'
        
        for file in os.listdir(current_dir):
            if file.endswith(('.xlsx', '.xls')):
                file_path = os.path.join(current_dir, file)
                file_time = os.path.getmtime(file_path)
                
                # 设置优先级：用户上传的瞬断文件 > 其他瞬断文件 > 默认文件 > 其他文件
                if '频繁瞬断' in file and file != default_file:
                    priority = 3  # 用户上传的瞬断文件最高优先级
                elif '频繁瞬断' in file and file == default_file:
                    priority = 1  # 默认文件较低优先级
                elif '瞬断' in file:
                    priority = 2  # 其他瞬断文件中等优先级
                else:
                    priority = 0  # 其他文件最低优先级
                    
                excel_files.append((file, file_time, priority))
        
        if excel_files:
            # 按优先级和时间排序
            excel_files.sort(key=lambda x: (x[2], x[1]), reverse=True)
            selected_file = excel_files[0]
            
            print(f"✅ 系统将选择文件: {selected_file[0]}")
            print(f"   优先级: {selected_file[2]}")
            print(f"   修改时间: {datetime.fromtimestamp(selected_file[1]).strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 显示前5个文件的优先级
            print("\n📋 文件优先级排序（前5个）:")
            for i, (file, file_time, priority) in enumerate(excel_files[:5], 1):
                status = "🎯 [将被选择]" if i == 1 else ""
                print(f"   {i}. {file} (优先级: {priority}) {status}")
            
            return selected_file[0] != default_file
        else:
            print("❌ 未找到任何Excel文件")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 瞬断模块默认数据清理工具")
    print("=" * 60)
    print("目标: 删除默认瞬断数据，确保系统优先使用用户导入的数据")
    print("=" * 60)
    
    # 步骤1: 删除默认文件
    files_removed = remove_default_outage_files()
    
    # 步骤2: 检查用户文件
    user_files_exist = check_user_outage_files()
    
    # 步骤3: 验证系统优先级
    priority_correct = verify_system_priority()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 清理完成总结:")
    print("=" * 60)
    
    if files_removed:
        print("✅ 默认瞬断文件已成功删除")
    else:
        print("ℹ️  没有找到需要删除的默认文件")
    
    if user_files_exist:
        print("✅ 检测到用户上传的瞬断文件")
    else:
        print("⚠️  未检测到用户瞬断文件，建议上传")
    
    if priority_correct:
        print("✅ 系统优先级设置正确，将使用用户文件")
    else:
        print("⚠️  系统可能仍会使用默认文件")
    
    print("\n💡 使用建议:")
    print("1. 重启Web应用以确保更改生效")
    print("2. 在瞬断分析页面测试功能")
    print("3. 检查控制台日志确认使用的文件")
    print("4. 如需恢复默认文件，可从backup_default_files目录恢复")
    
    print("\n🔄 重启应用命令:")
    print("   python web_app.py")

if __name__ == "__main__":
    main()
