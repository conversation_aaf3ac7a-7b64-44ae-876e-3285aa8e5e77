#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理所有默认数据源
确保系统不使用任何默认数据，只使用用户导入的数据
"""

import os
import shutil
import sqlite3
from datetime import datetime

def backup_and_remove_files():
    """备份并删除默认数据文件"""
    print("🗑️  清理默认数据文件...")
    print("=" * 50)
    
    # 创建备份目录
    backup_dir = 'backup_all_default_data'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"✅ 创建备份目录: {backup_dir}")
    
    # 需要删除的默认数据文件
    default_files = [
        # 瞬断相关文件
        '8月瞬断告警.xlsx',
        '频繁瞬断分析_包含天数.xlsx',
        '频繁瞬断分析_包含最近日期.xlsx',
        '频繁瞬断分析_居中对齐.xlsx',
        '频繁瞬断分析_日期加粗.xlsx',
        
        # 扇区数据文件
        '扇区粒度退服故障统计-7.31.xlsx',
        '扇区粒度退服故障统计-8.1.xlsx',
        '扇区粒度退服故障统计-8.2.xlsx',
        '扇区粒度退服故障统计-8.3.xlsx',
        '扇区粒度退服故障统计-8.4.xlsx',
        '扇区粒度退服故障统计-8.5.xlsx',
        '扇区粒度退服故障统计-月度-7.31.xlsx',
        '扇区粒度退服故障统计-月度-8.1.xlsx',
        '扇区粒度退服故障统计-月度-8.2.xlsx',
        '扇区粒度退服故障统计-月度-8.3.xlsx',
        '扇区粒度退服故障统计-月度-8.4.xlsx',
        '扇区粒度退服故障统计-月度-8.5.xlsx',
        
        # 天粒度数据文件
        '扇区粒度退服故障统计清单-天粒度-6.17.xlsx',
        '扇区粒度退服故障统计清单-天粒度-6.18.xlsx',
        '扇区粒度退服故障统计清单-天粒度-7.2.xlsx',
        '扇区粒度退服故障统计清单-天粒度-7.31.xlsx',
        '扇区粒度退服故障统计清单-天粒度-8.1.xlsx',
        '扇区粒度退服故障统计清单-天粒度-8.2.xlsx',
        '扇区粒度退服故障统计清单-天粒度-8.3.xlsx',
        '扇区粒度退服故障统计清单-天粒度-8.4.xlsx',
        '扇区粒度退服故障统计清单-天粒度-8.5.xlsx',
        '扇区粒度退服故障统计清单-天粒度.xlsx',
        
        # 其他数据文件
        '20258.xlsx',
        '78.4.xlsx',
        '8.xlsx',
        'test_export.xlsx',
        '菏泽_【月累计】明细_1月.xlsx',
        '菏泽联通4G现网工参-2025-08-11.xlsx',
        '菏泽联通5G-现网工参-2025-08-11.xlsx',
        
        # 映射文件
        '20258_mapping.json',
        '78.4_mapping.json',
        '8_mapping.json'
    ]
    
    removed_files = []
    backed_up_files = []
    
    for file_name in default_files:
        if os.path.exists(file_name):
            try:
                # 备份文件
                backup_path = os.path.join(backup_dir, f"{file_name}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                shutil.copy2(file_name, backup_path)
                backed_up_files.append((file_name, backup_path))
                print(f"📦 备份: {file_name}")
                
                # 删除原文件
                os.remove(file_name)
                removed_files.append(file_name)
                print(f"🗑️  删除: {file_name}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_name} 时出错: {str(e)}")
        else:
            print(f"ℹ️  文件不存在: {file_name}")
    
    print(f"\n📊 文件清理结果:")
    print(f"   删除文件: {len(removed_files)} 个")
    print(f"   备份文件: {len(backed_up_files)} 个")
    
    return len(removed_files)

def clear_database():
    """清理数据库中的默认数据"""
    print("\n🗄️  清理数据库默认数据...")
    print("=" * 50)
    
    db_path = 'heze_data.db'
    
    if not os.path.exists(db_path):
        print(f"ℹ️  数据库文件不存在: {db_path}")
        return 0
    
    try:
        # 备份数据库
        backup_db = f'backup_all_default_data/heze_data.db.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        shutil.copy2(db_path, backup_db)
        print(f"📦 数据库已备份: {backup_db}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        cleared_tables = []
        
        for table_name, in tables:
            if table_name != 'sqlite_sequence':  # 跳过系统表
                try:
                    # 获取表中的记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    
                    if count > 0:
                        # 清空表
                        cursor.execute(f"DELETE FROM {table_name}")
                        cleared_tables.append((table_name, count))
                        print(f"🗑️  清空表: {table_name} ({count}条记录)")
                    else:
                        print(f"ℹ️  表已为空: {table_name}")
                        
                except Exception as e:
                    print(f"❌ 清理表 {table_name} 失败: {str(e)}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n📊 数据库清理结果:")
        print(f"   清空表数: {len(cleared_tables)} 个")
        print(f"   删除记录: {sum(count for _, count in cleared_tables)} 条")
        
        return len(cleared_tables)
        
    except Exception as e:
        print(f"❌ 数据库清理失败: {str(e)}")
        return 0

def clear_upload_cache():
    """清理上传缓存"""
    print("\n📁 清理上传缓存...")
    print("=" * 50)
    
    cache_dirs = ['uploads', 'temp', 'attachments', 'exports']
    cleared_items = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                # 备份目录
                backup_path = f'backup_all_default_data/{cache_dir}_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
                shutil.copytree(cache_dir, backup_path)
                print(f"📦 备份目录: {cache_dir} -> {backup_path}")
                
                # 清空目录内容
                for item in os.listdir(cache_dir):
                    item_path = os.path.join(cache_dir, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                        cleared_items += 1
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                        cleared_items += 1
                
                print(f"🗑️  清空目录: {cache_dir}")
                
            except Exception as e:
                print(f"❌ 清理目录 {cache_dir} 失败: {str(e)}")
        else:
            print(f"ℹ️  目录不存在: {cache_dir}")
    
    print(f"\n📊 缓存清理结果:")
    print(f"   清理项目: {cleared_items} 个")
    
    return cleared_items

def update_system_config():
    """更新系统配置，禁用默认数据"""
    print("\n⚙️  更新系统配置...")
    print("=" * 50)
    
    try:
        # 检查web_app.py中是否有硬编码的默认文件路径
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找可能的默认文件引用
        default_references = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            if any(keyword in line.lower() for keyword in ['默认', 'default', '固定']):
                if any(ext in line for ext in ['.xlsx', '.xls', '.db']):
                    default_references.append((i, line.strip()))
        
        if default_references:
            print(f"🔍 发现可能的默认数据引用:")
            for line_num, line_content in default_references[:10]:  # 只显示前10个
                print(f"   行{line_num}: {line_content[:80]}...")
            
            if len(default_references) > 10:
                print(f"   ... 还有{len(default_references) - 10}个引用")
        else:
            print(f"✅ 未发现硬编码的默认数据引用")
        
        print(f"✅ 系统配置检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统配置更新失败: {str(e)}")
        return False

def main():
    """主清理函数"""
    print("🧹 清理所有默认数据源")
    print("=" * 60)
    print("目标: 删除所有默认数据，确保系统只使用用户导入的数据")
    print("=" * 60)
    
    # 确认操作
    print("⚠️  警告: 此操作将删除所有默认数据文件和数据库内容")
    print("📦 所有文件都会先备份到 backup_all_default_data 目录")
    
    user_input = input("\n确认继续? (输入 'YES' 确认): ").strip()
    
    if user_input != 'YES':
        print("❌ 操作已取消")
        return
    
    print("\n🚀 开始清理...")
    
    # 步骤1: 清理文件
    files_removed = backup_and_remove_files()
    
    # 步骤2: 清理数据库
    tables_cleared = clear_database()
    
    # 步骤3: 清理缓存
    cache_cleared = clear_upload_cache()
    
    # 步骤4: 更新配置
    config_updated = update_system_config()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 清理完成总结:")
    print("=" * 60)
    
    print(f"✅ 删除默认文件: {files_removed} 个")
    print(f"✅ 清空数据表: {tables_cleared} 个")
    print(f"✅ 清理缓存项: {cache_cleared} 个")
    print(f"✅ 配置检查: {'通过' if config_updated else '失败'}")
    
    total_items = files_removed + tables_cleared + cache_cleared
    print(f"\n📊 总计清理: {total_items} 项")
    
    if total_items > 0:
        print(f"\n🎉 清理完成!")
        print(f"✅ 系统现在是完全干净的状态")
        print(f"✅ 不会使用任何默认数据")
        print(f"✅ 所有数据都已安全备份")
        
        print(f"\n💡 下一步:")
        print(f"1. 重启Web应用: python web_app.py")
        print(f"2. 上传你的数据文件")
        print(f"3. 系统会只使用你导入的数据")
        print(f"4. 如需恢复，可从backup_all_default_data目录恢复")
        
    else:
        print(f"\n✅ 系统已经是干净状态")
        print(f"ℹ️  没有找到需要清理的默认数据")

if __name__ == "__main__":
    main()
