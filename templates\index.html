{% extends "base.html" %}

{% block styles %}
<style>
.upload-area-small {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-area-small:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-area-small.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

.file-info-small {
    padding: 15px;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-outline-primary:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover,
.btn-outline-success:hover,
.btn-outline-secondary:hover,
.btn-outline-dark:hover {
    transform: translateY(-1px);
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

@media (max-width: 768px) {
    .upload-area-small {
        min-height: 100px;
        padding: 15px;
    }

    .file-info-small {
        min-height: 100px;
        padding: 10px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-5 text-primary mb-2">
                    <i class="bi bi-speedometer2"></i> 菏泽数据处理中心
                </h1>
                <p class="text-muted">一站式数据处理工作台</p>
            </div>
        </div>
    </div>



    <!-- 数据上传与处理区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-upload"></i> 数据上传与处理</h5>
                    <small class="text-muted">三种数据可任意顺序上传，无先后要求</small>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('upload_files') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                        <div class="row">
                            <!-- 扇区数据上传 -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-primary">
                                    <div class="card-header bg-primary text-white text-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-database"></i> 扇区数据
                                        </h6>
                                    </div>
                                    <div class="card-body p-3">
                                        <div class="upload-area-small" id="sectorUploadArea">
                                            <i class="bi bi-cloud-upload text-muted mb-2" style="font-size: 2rem;"></i>
                                            <p class="small text-muted mb-2">点击或拖拽上传</p>
                                            <input type="file" class="form-control d-none" id="sector_file" name="sector_file" accept=".xlsx,.xls">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('sector_file').click()">
                                                <i class="bi bi-folder2-open"></i> 选择文件
                                            </button>
                                        </div>
                                        <div id="sectorFileInfo" class="file-info-small d-none">
                                            <div class="text-center">
                                                <i class="bi bi-file-earmark-excel text-success"></i>
                                                <p class="small mb-1" id="sectorFileName"></p>
                                                <p class="small text-success mb-1">✅ 已上传</p>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile('sector')">
                                                    <i class="bi bi-arrow-clockwise"></i> 重新上传
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 日度文件上传 -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-info">
                                    <div class="card-header bg-info text-white text-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-calendar-day"></i> 日度数据
                                        </h6>
                                    </div>
                                    <div class="card-body p-3">
                                        <div class="upload-area-small" id="dailyUploadArea">
                                            <i class="bi bi-cloud-upload text-muted mb-2" style="font-size: 2rem;"></i>
                                            <p class="small text-muted mb-2">点击或拖拽上传</p>
                                            <input type="file" class="form-control d-none" id="daily_file" name="daily_file" accept=".xlsx,.xls">
                                            <button type="button" class="btn btn-outline-info btn-sm" onclick="document.getElementById('daily_file').click()">
                                                <i class="bi bi-folder2-open"></i> 选择文件
                                            </button>
                                        </div>
                                        <div id="dailyFileInfo" class="file-info-small d-none">
                                            <div class="text-center">
                                                <i class="bi bi-file-earmark-excel text-success"></i>
                                                <p class="small mb-1" id="dailyFileName"></p>
                                                <p class="small text-success mb-1">✅ 已上传</p>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile('daily')">
                                                    <i class="bi bi-arrow-clockwise"></i> 重新上传
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 月度文件上传 -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-warning">
                                    <div class="card-header bg-warning text-dark text-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-calendar-month"></i> 月度数据
                                        </h6>
                                    </div>
                                    <div class="card-body p-3">
                                        <div class="upload-area-small" id="monthlyUploadArea">
                                            <i class="bi bi-cloud-upload text-muted mb-2" style="font-size: 2rem;"></i>
                                            <p class="small text-muted mb-2">点击或拖拽上传</p>
                                            <input type="file" class="form-control d-none" id="monthly_file" name="monthly_file" accept=".xlsx,.xls">
                                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="document.getElementById('monthly_file').click()">
                                                <i class="bi bi-folder2-open"></i> 选择文件
                                            </button>
                                        </div>
                                        <div id="monthlyFileInfo" class="file-info-small d-none">
                                            <div class="text-center">
                                                <i class="bi bi-file-earmark-excel text-success"></i>
                                                <p class="small mb-1" id="monthlyFileName"></p>
                                                <p class="small text-success mb-1">✅ 已上传</p>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile('monthly')">
                                                    <i class="bi bi-arrow-clockwise"></i> 重新上传
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 处理控制区域 -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="text-center">
                                    <button type="submit" class="btn btn-success btn-lg" id="submitBtn" disabled>
                                        <i class="bi bi-gear"></i> 开始处理数据
                                    </button>
                                    <div class="mt-2">
                                        <small class="text-muted" id="submitHint">
                                            <i class="bi bi-info-circle"></i> 请至少上传一个文件才能开始处理
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具箱 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-tools"></i> 工具箱</h5>
                </div>
                <div class="card-body">
                    <div class="row justify-content-center">
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/data_manager" class="btn btn-outline-danger w-100">
                                <i class="bi bi-database-gear"></i><br>
                                <small>数据管理</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/sector_data_manager" class="btn btn-outline-primary w-100">
                                <i class="bi bi-database"></i><br>
                                <small>扇区数据管理</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/repeated_outages" class="btn btn-outline-warning w-100">
                                <i class="bi bi-arrow-repeat"></i><br>
                                <small>重复故障分析</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/grid_monthly_stats" class="btn btn-outline-info w-100">
                                <i class="bi bi-graph-up"></i><br>
                                <small>网格月度统计</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/custom_period_repeated_outages" class="btn btn-outline-success w-100">
                                <i class="bi bi-calendar-range"></i><br>
                                <small>时间范围重复故障</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/email_config" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-envelope-gear"></i><br>
                                <small>邮件配置</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/server_config" class="btn btn-outline-primary w-100">
                                <i class="bi bi-server"></i><br>
                                <small>服务器配置</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/smart_data_analysis" class="btn btn-outline-success w-100">
                                <i class="bi bi-cpu"></i><br>
                                <small>智能数据分析</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/network_params_manager" class="btn btn-outline-warning w-100">
                                <i class="bi bi-gear"></i><br>
                                <small>工参管理</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="/frequent_outage_analyzer" class="btn btn-outline-danger w-100">
                                <i class="bi bi-lightning"></i><br>
                                <small>频繁瞬断分析</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <button class="btn btn-outline-dark w-100" onclick="showAboutModal()">
                                <i class="bi bi-info-circle"></i><br>
                                <small>关于系统</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setupFileUploads();
});



// 设置文件上传
function setupFileUploads() {
    setupFileUpload('sector_file', 'sectorUploadArea', 'sectorFileInfo', 'sectorFileName');
    setupFileUpload('daily_file', 'dailyUploadArea', 'dailyFileInfo', 'dailyFileName');
    setupFileUpload('monthly_file', 'monthlyUploadArea', 'monthlyFileInfo', 'monthlyFileName');
}

// 文件上传处理（适配新布局）
function setupFileUpload(inputId, areaId, infoId, fileNameId) {
    const input = document.getElementById(inputId);
    const area = document.getElementById(areaId);
    const info = document.getElementById(infoId);
    const fileName = document.getElementById(fileNameId);

    // 点击上传区域触发文件选择
    area.addEventListener('click', function(e) {
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            return;
        }
        input.click();
    });

    // 文件选择处理
    input.addEventListener('change', function() {
        if (this.files.length > 0) {
            const file = this.files[0];
            fileName.textContent = file.name;
            info.classList.remove('d-none');
            area.style.display = 'none';

            // 注释掉扇区文件的特殊处理，让它通过主表单提交
            // if (inputId === 'sector_file') {
            //     uploadSectorFile(file);
            // }

            checkSubmitButton();
        }
    });

    // 拖拽处理
    area.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    area.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    area.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                input.files = files;
                fileName.textContent = file.name;
                info.classList.remove('d-none');
                area.style.display = 'none';

                // 注释掉扇区文件的特殊处理，让它通过主表单提交
                // if (inputId === 'sector_file') {
                //     uploadSectorFile(file);
                // }

                checkSubmitButton();
            } else {
                showAlert('请上传Excel文件（.xlsx或.xls格式）', 'warning');
            }
        }
    });
}

// 上传扇区文件
function uploadSectorFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    // 显示上传状态
    const statusElement = document.getElementById('sectorFileInfo');
    statusElement.innerHTML = `
        <div class="text-center">
            <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
            <p class="small mb-1">正在上传...</p>
        </div>
    `;

    fetch('/api/upload_sector_file', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.innerHTML = `
                <div class="text-center">
                    <i class="bi bi-file-earmark-excel text-success"></i>
                    <p class="small mb-1">${file.name}</p>
                    <p class="small text-success mb-1">✅ 上传成功</p>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile('sector')">
                        <i class="bi bi-arrow-clockwise"></i> 重新上传
                    </button>
                </div>
            `;
        } else {
            statusElement.innerHTML = `
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    <p class="small mb-1">上传失败</p>
                    <p class="small text-danger mb-1">${data.error}</p>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="clearFile('sector')">
                        <i class="bi bi-arrow-clockwise"></i> 重试
                    </button>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('扇区文件上传失败:', error);
        statusElement.innerHTML = `
            <div class="text-center">
                <i class="bi bi-exclamation-triangle text-danger"></i>
                <p class="small mb-1">网络错误</p>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="clearFile('sector')">
                    <i class="bi bi-arrow-clockwise"></i> 重试
                </button>
            </div>
        `;
    });
}

// 清除文件
function clearFile(type) {
    const input = document.getElementById(type + '_file');
    const area = document.getElementById(type + 'UploadArea');
    const info = document.getElementById(type + 'FileInfo');

    input.value = '';
    info.classList.add('d-none');
    area.style.display = 'block';
    checkSubmitButton();
}

// 检查提交按钮状态
function checkSubmitButton() {
    const dailyFile = document.getElementById('daily_file').files.length > 0;
    const monthlyFile = document.getElementById('monthly_file').files.length > 0;
    const sectorFile = document.getElementById('sector_file').files.length > 0;
    const submitBtn = document.getElementById('submitBtn');
    const submitHint = document.getElementById('submitHint');

    // 如果按钮正在处理中，不要改变状态
    if (submitBtn.innerHTML.includes('处理中')) {
        return;
    }

    if (dailyFile || monthlyFile || sectorFile) {
        submitBtn.disabled = false;
        submitBtn.className = 'btn btn-success btn-lg';
        submitBtn.innerHTML = '<i class="bi bi-gear"></i> 开始处理数据';
        submitHint.innerHTML = '<i class="bi bi-check-circle text-success"></i> 可以开始处理数据';
    } else {
        submitBtn.disabled = true;
        submitBtn.className = 'btn btn-secondary btn-lg';
        submitBtn.innerHTML = '<i class="bi bi-gear"></i> 开始处理数据';
        submitHint.innerHTML = '<i class="bi bi-info-circle"></i> 请至少上传一个文件才能开始处理';
    }
}



// 显示关于模态框
function showAboutModal() {
    showAlert('菏泽数据处理中心 v2.1<br>开发者：徐嘉昕<br>中国联通菏泽市分公司', 'info');
}

// 显示提示消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 表单提交处理
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');

    // 防止重复提交
    if (submitBtn.disabled || submitBtn.innerHTML.includes('处理中')) {
        e.preventDefault();
        return false;
    }

    // 验证文件
    const dailyFile = document.getElementById('daily_file').files.length > 0;
    const monthlyFile = document.getElementById('monthly_file').files.length > 0;
    const sectorFile = document.getElementById('sector_file').files.length > 0;

    if (!dailyFile && !monthlyFile && !sectorFile) {
        e.preventDefault();
        showAlert('请至少上传一个文件', 'warning');
        return false;
    }

    // 禁用按钮防止重复提交
    submitBtn.disabled = true;
    submitBtn.className = 'btn btn-warning btn-lg';
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';

    // 更新提示文本
    document.getElementById('submitHint').innerHTML = '<i class="bi bi-hourglass-split"></i> 正在处理数据，请稍候...';

    // 添加处理中的视觉反馈
    document.body.style.cursor = 'wait';
});
</script>
{% endblock %}
