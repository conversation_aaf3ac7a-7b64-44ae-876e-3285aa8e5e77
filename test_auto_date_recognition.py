#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断模块自动日期识别功能
"""

import requests
import json
import time
import subprocess
import os
from datetime import datetime

def start_web_server():
    """启动Web服务器"""
    print("🚀 启动Web服务器...")
    print("-" * 40)
    
    try:
        # 检查服务器是否已经运行
        try:
            response = requests.get('http://localhost:5000', timeout=2)
            print("✅ Web服务器已在运行")
            return True
        except:
            pass
        
        print("🔄 正在启动Web服务器...")
        print("💡 请在另一个终端运行: python web_app.py")
        print("⏳ 等待服务器启动...")
        
        # 等待用户启动服务器
        for i in range(30):
            try:
                response = requests.get('http://localhost:5000', timeout=1)
                print("✅ Web服务器启动成功!")
                return True
            except:
                time.sleep(1)
                if i % 5 == 0:
                    print(f"   等待中... ({i+1}/30秒)")
        
        print("❌ Web服务器启动超时")
        return False
        
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {str(e)}")
        return False

def test_date_range_api():
    """测试时间范围API"""
    print("\n🔍 测试时间范围API...")
    print("-" * 40)
    
    try:
        url = "http://localhost:5000/api/get_outage_date_range"
        print(f"📡 请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📈 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ API调用成功!")
                
                date_range = data.get('date_range', {})
                print(f"📅 时间范围:")
                print(f"   开始日期: {date_range.get('start', 'N/A')}")
                print(f"   结束日期: {date_range.get('end', 'N/A')}")
                print(f"   数据源: {data.get('source', 'N/A')}")
                print(f"   文件名: {data.get('filename', 'N/A')}")
                print(f"   记录数: {data.get('total_records', 0)}")
                
                return data
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到服务器")
        return None
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return None

def test_outage_analysis_with_auto_dates():
    """测试使用自动识别日期的瞬断分析"""
    print("\n📊 测试使用自动识别日期的瞬断分析...")
    print("-" * 40)
    
    # 首先获取自动识别的日期范围
    date_data = test_date_range_api()
    
    if not date_data:
        print("❌ 无法获取日期范围，跳过分析测试")
        return False
    
    # 使用自动识别的日期进行分析
    try:
        url = "http://localhost:5000/api/frequent_outage/analyze"
        
        test_data = {
            "start_date": date_data['date_range']['start'],
            "end_date": date_data['date_range']['end']
        }
        
        print(f"📡 发送分析请求:")
        print(f"   开始日期: {test_data['start_date']}")
        print(f"   结束日期: {test_data['end_date']}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 瞬断分析成功!")
                
                records = result.get('records', [])
                summary = result.get('summary', {})
                
                print(f"📊 分析结果:")
                print(f"   总记录数: {len(records)}")
                print(f"   总瞬断次数: {summary.get('total_outages', 0)}")
                print(f"   涉及站点数: {summary.get('total_sites', 0)}")
                
                # 显示前3条记录
                if records:
                    print(f"\n📋 前3条记录:")
                    for i, record in enumerate(records[:3], 1):
                        print(f"   {i}. {record.get('小区名称', 'N/A')[:40]}...")
                        print(f"      网格: {record.get('归属网格', 'N/A')}")
                        print(f"      瞬断次数: {record.get('瞬断次数', 0)}")
                        print(f"      瞬断天数: {record.get('瞬断天数', 0)}")
                
                return True
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 分析测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 瞬断模块自动日期识别功能测试")
    print("=" * 60)
    print("验证自动日期识别和数据处理功能...")
    print("=" * 60)
    
    # 检查服务器状态
    server_running = start_web_server()
    
    if not server_running:
        print("\n❌ Web服务器未运行，无法进行完整测试")
        print("💡 请先启动服务器: python web_app.py")
        return
    
    # 测试时间范围API
    date_api_ok = test_date_range_api() is not None
    
    # 测试完整的瞬断分析
    analysis_ok = test_outage_analysis_with_auto_dates()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 自动日期识别测试总结:")
    print("=" * 60)
    
    if date_api_ok and analysis_ok:
        print("🎉 所有测试通过!")
        print("✅ 自动日期识别功能正常")
        print("✅ 瞬断分析功能正常")
        print("✅ 系统正确使用用户数据")
        
        print("\n💡 使用说明:")
        print("1. 打开瞬断分析页面")
        print("2. 页面会自动识别数据时间范围并设置日期")
        print("3. 显示提示信息: '🎯 已自动识别时间范围: 2025-08-01 至 2025-08-13'")
        print("4. 点击分析按钮即可获得正确结果")
        print("5. 无需手动调整开始和结束日期")
        
    else:
        print("❌ 部分测试失败")
        
        if not date_api_ok:
            print("❌ 时间范围API测试失败")
        if not analysis_ok:
            print("❌ 瞬断分析测试失败")
        
        print("\n💡 故障排除:")
        print("1. 确保Web服务器正在运行")
        print("2. 检查瞬断数据文件是否存在")
        print("3. 验证文件格式是否正确")
        print("4. 查看服务器控制台日志")

if __name__ == "__main__":
    main()
