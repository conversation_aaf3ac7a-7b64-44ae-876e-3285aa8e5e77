#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瞬断模块上传功能
"""

import requests
import os
from datetime import datetime

def test_upload_api():
    """测试上传API"""
    print("🧪 测试瞬断文件上传API")
    print("=" * 50)
    
    # 检查文件
    filename = '8月瞬断告警.xlsx'
    if not os.path.exists(filename):
        print(f"❌ 测试文件不存在: {filename}")
        return False
    
    file_size = os.path.getsize(filename)
    print(f"📄 测试文件: {filename}")
    print(f"📊 文件大小: {file_size:,} 字节")
    
    try:
        # 准备上传
        url = "http://localhost:5000/api/frequent_outage/upload"
        
        with open(filename, 'rb') as f:
            files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            print(f"📡 上传到: {url}")
            print(f"⏳ 正在上传...")
            
            response = requests.post(url, files=files, timeout=60)
            
            print(f"📈 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print("✅ 上传成功!")
                    
                    # 检查分析结果
                    if result.get('analysis_data'):
                        analysis = result['analysis_data']
                        records = analysis.get('records', [])
                        summary = analysis.get('summary', {})
                        
                        print(f"📊 自动分析结果:")
                        print(f"   分析记录数: {len(records)}")
                        print(f"   总瞬断次数: {summary.get('total_outages', 0)}")
                        print(f"   涉及站点数: {summary.get('total_sites', 0)}")
                        
                        # 检查日期范围
                        if result.get('date_range'):
                            date_range = result['date_range']
                            print(f"📅 自动识别时间范围:")
                            print(f"   开始日期: {date_range.get('start', 'N/A')}")
                            print(f"   结束日期: {date_range.get('end', 'N/A')}")
                        
                        return True
                    else:
                        print(f"⚠️  上传成功但未自动分析")
                        if result.get('analysis_error'):
                            print(f"   分析错误: {result['analysis_error']}")
                        return True
                else:
                    print(f"❌ 上传失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到服务器")
        print("💡 请确保Web应用正在运行: python web_app.py")
        return False
    except Exception as e:
        print(f"❌ 上传测试失败: {str(e)}")
        return False

def test_page_features():
    """测试页面功能"""
    print("\n🌐 测试页面功能...")
    print("=" * 50)
    
    try:
        # 测试页面访问
        response = requests.get('http://localhost:5000/frequent_outage_analyzer', timeout=10)
        
        if response.status_code == 200:
            print("✅ 瞬断分析页面访问正常")
            
            # 检查页面内容
            content = response.text
            
            features = [
                ('上传区域', '上传瞬断数据文件' in content),
                ('拖拽提示', '拖拽文件到此处' in content or '拖拽Excel文件' in content),
                ('使用说明', '使用说明' in content),
                ('日期输入', 'startDate' in content and 'endDate' in content),
                ('分析按钮', '开始分析' in content),
                ('自动识别', 'loadDateRange' in content)
            ]
            
            print(f"📋 页面功能检查:")
            for feature_name, exists in features:
                status = "✅" if exists else "❌"
                print(f"   {status} {feature_name}")
            
            all_features_ok = all(exists for _, exists in features)
            return all_features_ok
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面测试失败: {str(e)}")
        return False

def create_upload_guide():
    """创建上传使用指南"""
    print("\n📖 创建上传使用指南...")
    print("=" * 50)
    
    guide = """# 瞬断数据上传使用指南

## 🎯 两种使用方式

### 方式1: 📤 上传新文件 (推荐)
1. **打开瞬断分析页面**
2. **找到上传区域** (蓝色虚线框)
3. **上传方式**:
   - 🖱️ 点击上传区域选择文件
   - 🖱️ 直接拖拽Excel文件到上传区域
4. **自动处理**: 上传后系统会自动分析并设置时间范围
5. **查看结果**: 直接显示分析结果

### 方式2: 🎯 使用已有文件 (自动识别)
1. **确保文件在目录中**: 将Excel文件放在应用根目录
2. **打开瞬断分析页面**: 系统会自动识别最新文件
3. **自动设置日期**: 根据文件内容自动设置时间范围
4. **开始分析**: 点击"开始分析"按钮

## 📊 文件要求

### **支持格式**
- ✅ Excel 2007+ (.xlsx)
- ✅ Excel 97-2003 (.xls)

### **必要列名**
- ✅ `网元名称` (或 `小区名称`、`基站名称`)
- ✅ `告警发生时间` (或 `告警产生时间`、`告警发现时间`)

### **可选列名**
- 📱 `网管设备类型` (网络制式)
- 🏢 `是否为室分系统信号源` (室分信息)
- 🕐 `告警清除时间` (清除时间)

## 🎯 自动识别规则

### **文件优先级**
```
优先级 4: 瞬断告警数据 (如: 8月瞬断告警.xlsx) ⭐ 最高
优先级 3: 用户频繁瞬断文件
优先级 2: 其他瞬断文件  
优先级 1: 默认文件 (已删除)
优先级 0: 其他Excel文件
```

### **时间范围识别**
- 🔍 **自动扫描**: 读取文件中所有时间记录
- 📅 **智能识别**: 自动确定最早和最晚日期
- 🌐 **跨月支持**: 支持任意月份、跨月、跨年数据
- 📊 **统计分析**: 显示月份分布、天数跨度等信息

## 💡 使用建议

### **推荐流程**
1. **使用上传功能**: 直接在页面上传最新的瞬断告警文件
2. **等待自动处理**: 系统会自动分析并设置时间范围
3. **查看识别结果**: 确认文件名、记录数、时间范围
4. **开始分析**: 点击分析按钮获得结果

### **文件命名建议**
- ✅ 包含"瞬断告警"关键词 (最高优先级)
- ✅ 包含月份信息 (如: 8月瞬断告警.xlsx)
- ✅ 使用描述性名称 (如: 2025年8月瞬断告警数据.xlsx)

### **故障排除**
- 🔍 **上传无反应**: 检查文件格式是否为Excel
- 📊 **处理结果为0**: 确认文件包含必要的列名
- 🕐 **时间范围错误**: 检查时间列数据格式
- 🌐 **网元识别失败**: 确认网元名称列存在且有数据

## 🎉 预期效果

上传你的瞬断告警文件后，系统会：
- 📄 **自动识别文件**: 显示文件名和基本信息
- 📅 **自动设置日期**: 根据数据内容设置开始和结束日期
- 📊 **自动分析**: 立即进行瞬断分析并显示结果
- 💬 **智能提示**: 显示详细的识别和处理信息

---

**🚀 现在就可以试试上传功能！**
"""
    
    with open('瞬断上传使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 上传使用指南已保存到: 瞬断上传使用指南.md")
    return True

def main():
    """主函数"""
    print("🧪 瞬断模块上传功能测试")
    print("=" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get('http://localhost:5000', timeout=2)
        server_running = True
        print("✅ Web服务器正在运行")
    except:
        server_running = False
        print("❌ Web服务器未运行")
        print("💡 请先启动: python web_app.py")
    
    # 测试页面功能
    if server_running:
        page_ok = test_page_features()
        
        # 询问是否测试上传
        print(f"\n❓ 是否测试文件上传功能？")
        print(f"⚠️  注意: 这会实际上传文件到服务器")
        user_input = input("输入 y 继续，其他键跳过: ").lower().strip()
        
        if user_input == 'y':
            upload_ok = test_upload_api()
        else:
            upload_ok = None
            print("⏭️  跳过上传测试")
    else:
        page_ok = False
        upload_ok = None
    
    # 创建指南
    guide_created = create_upload_guide()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print("=" * 60)
    
    if server_running and page_ok:
        print("✅ 瞬断分析页面功能完整")
        print("✅ 上传区域已优化，更加明显")
        print("✅ 支持点击上传和拖拽上传")
        print("✅ 自动识别和分析功能正常")
        
        if upload_ok:
            print("✅ 文件上传功能测试通过")
        elif upload_ok is None:
            print("⏭️  文件上传功能未测试")
        else:
            print("❌ 文件上传功能测试失败")
        
        print(f"\n💡 现在你可以:")
        print(f"1. 📤 在页面上传新的瞬断数据文件")
        print(f"2. 🎯 让系统自动识别已有文件")
        print(f"3. 📅 自动设置为数据的实际时间范围")
        print(f"4. 📊 获得准确的分析结果")
        
    else:
        print("❌ 部分功能测试失败")
        print("💡 请确保Web服务器正常运行")

if __name__ == "__main__":
    main()
