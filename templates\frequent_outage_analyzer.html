<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频繁瞬断分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .analysis-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: end;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .summary-card h3 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 5px;
        }

        .summary-card p {
            color: #6c757d;
            font-size: 0.9em;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .table-header h3 {
            color: #495057;
            margin-bottom: 5px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .navigation {
            margin-bottom: 20px;
        }

        .nav-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .nav-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 频繁瞬断分析工具</h1>
            <p>分析网元瞬断情况，匹配归属网格，识别故障模式</p>
        </div>

        <div class="content">
            <div class="navigation">
                <a href="/" class="nav-link">← 返回主页</a>
            </div>

            <div class="analysis-form">
                <h3 style="margin-bottom: 20px; color: #495057;">📊 频繁瞬断分析</h3>

                <!-- 使用说明 -->
                <div class="alert alert-primary" style="margin-bottom: 20px; border-left: 4px solid #007bff;">
                    <h6 style="margin-bottom: 10px; color: #007bff;">💡 使用说明</h6>
                    <ul style="margin-bottom: 0; padding-left: 20px;">
                        <li><strong>方式1</strong>: 📤 上传新的瞬断数据文件 (推荐)</li>
                        <li><strong>方式2</strong>: 🎯 使用已有数据文件 (系统自动识别)</li>
                        <li><strong>支持格式</strong>: Excel文件 (.xlsx/.xls)</li>
                        <li><strong>必要列</strong>: 网元名称、告警发生时间</li>
                    </ul>
                </div>

                <!-- 文件上传区域 -->
                <div class="form-group" style="margin-bottom: 25px;">
                    <label style="font-weight: bold; color: #007bff; margin-bottom: 10px; display: block;">
                        📤 上传瞬断数据文件 (可选)
                    </label>
                    <div class="upload-area" id="uploadArea" style="
                        border: 2px dashed #007bff;
                        border-radius: 8px;
                        padding: 30px;
                        background-color: #f8f9fa;
                        text-align: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        position: relative;
                    " onclick="document.getElementById('fileUpload').click()">
                        <input type="file" class="custom-file-input" id="fileUpload" accept=".xlsx,.xls" onchange="handleFileUpload(this)" style="display: none;">
                        <div style="color: #007bff; font-size: 16px;">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                            <strong>📤 上传瞬断数据文件</strong>
                        </div>
                        <div style="color: #6c757d; margin-top: 8px;">
                            点击选择文件或拖拽Excel文件到此处
                        </div>
                        <div style="color: #6c757d; font-size: 12px; margin-top: 5px;">
                            支持 .xlsx / .xls 格式
                        </div>
                    </div>
                    <small class="form-text text-muted" style="margin-top: 8px;">
                        💡 支持瞬断告警原始数据，上传后会自动识别时间范围并分析
                    </small>
                </div>

                <!-- 当前文件信息 -->
                <div id="currentFileInfo" class="alert alert-success" style="margin-bottom: 25px; border-left: 4px solid #28a745;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <strong>📊 当前数据源:</strong> <span id="currentFileName">正在自动识别...</span>
                        </div>
                        <small class="text-muted" id="dataStats"></small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="startDate">开始日期 <small class="text-muted">(自动识别)</small></label>
                        <input type="date" id="startDate" class="form-control" value="2025-08-01">
                    </div>

                    <div class="form-group">
                        <label for="endDate">结束日期 <small class="text-muted">(自动识别)</small></label>
                        <input type="date" id="endDate" class="form-control" value="2025-08-13">
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="analyzeOutages()">
                            🔍 开始分析
                        </button>
                        <button type="button" id="exportBtn" class="btn btn-success ml-3" onclick="exportToExcel()" style="display: none;">
                            📊 导出Excel
                        </button>
                    </div>
                </div>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在分析频繁瞬断数据，请稍候...</p>
            </div>

            <div id="results" class="results-section">
                <div id="summaryCards" class="summary-cards">
                    <!-- 统计卡片将在这里动态生成 -->
                </div>

                <div class="table-container">
                    <div class="table-header">
                        <h3>📋 频繁瞬断详细分析结果</h3>
                        <p>显示每个网元的瞬断情况及归属网格信息</p>
                    </div>
                    
                    <div class="table-responsive">
                        <table id="resultsTable">
                            <thead>
                                <tr>
                                    <th>归属网格</th>
                                    <th>小区名称</th>
                                    <th>瞬断次数</th>
                                    <th>瞬断天数</th>
                                    <th>最近瞬断日期</th>
                                    <th>网络制式</th>
                                    <th>是否室分</th>
                                    <th>详细故障分布</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                                <!-- 数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分析频繁瞬断数据
        async function analyzeOutages() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                alert('请选择开始日期和结束日期');
                return;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                alert('开始日期不能晚于结束日期');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            try {
                const response = await fetch('/api/frequent_outage/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        start_date: startDate,
                        end_date: endDate
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.data);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('分析失败:', error);
                alert(`分析失败: ${error.message}`);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        // 显示分析结果
        function displayResults(data) {
            // 显示统计摘要
            displaySummaryCards(data.summary);
            
            // 显示详细表格
            displayResultsTable(data.records);
            
            // 显示结果区域
            document.getElementById('results').style.display = 'block';
        }
        
        // 显示统计卡片
        function displaySummaryCards(summary) {
            const container = document.getElementById('summaryCards');
            
            const cards = [
                {
                    title: summary.total_sites || 0,
                    subtitle: '受影响网元数',
                    color: '#667eea'
                },
                {
                    title: summary.total_outages || 0,
                    subtitle: '总瞬断次数',
                    color: '#e74c3c'
                },
                {
                    title: summary.avg_outages_per_site || 0,
                    subtitle: '平均每网元瞬断',
                    color: '#f39c12'
                },
                {
                    title: `${summary.indoor_ratio || 0}%`,
                    subtitle: '室分占比',
                    color: '#27ae60'
                }
            ];
            
            container.innerHTML = cards.map(card => `
                <div class="summary-card">
                    <h3 style="color: ${card.color}">${card.title}</h3>
                    <p>${card.subtitle}</p>
                </div>
            `).join('');
        }
        
        // 显示结果表格
        function displayResultsTable(records) {
            const tbody = document.getElementById('resultsTableBody');
            
            if (!records || records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            📭 没有找到符合条件的瞬断数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = records.map(record => `
                <tr>
                    <td><span class="badge badge-info">${record.归属网格}</span></td>
                    <td style="max-width: 300px; word-break: break-all;">${record.小区名称}</td>
                    <td><span class="badge ${getOutageBadgeClass(record.瞬断次数)}">${record.瞬断次数}次</span></td>
                    <td><span class="badge ${getDaysBadgeClass(record.瞬断天数)}">${record.瞬断天数}天</span></td>
                    <td><span class="badge ${getDateBadgeClass(record.最近瞬断日期)}">${record.最近瞬断日期}</span></td>
                    <td><span class="badge ${getTechBadgeClass(record.网络制式)}">${record.网络制式}</span></td>
                    <td><span class="badge ${record.是否室分 === '是' ? 'badge-warning' : 'badge-success'}">${record.是否室分}</span></td>
                    <td style="max-width: 280px; word-break: break-word; font-size: 0.75em; line-height: 1.4; padding: 8px;">
                        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 10px; border-radius: 6px; border-left: 4px solid #007bff; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <div style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace; color: #495057; white-space: pre-line; text-align: left;">
                                ${record.详细故障分布}
                            </div>
                        </div>
                    </td>
                </tr>
            `).join('');

            // 显示导出按钮
            document.getElementById('exportBtn').style.display = 'inline-block';
        }

        // 导出Excel功能
        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('请先选择日期范围并进行分析');
                return;
            }

            // 显示导出提示
            const exportBtn = document.getElementById('exportBtn');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '📊 导出中...';
            exportBtn.disabled = true;

            fetch('/api/frequent_outage/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate
                })
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('导出失败');
                }
            })
            .then(blob => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `频繁瞬断分析_${startDate}至${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 恢复按钮状态
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;

                // 显示成功提示
                showNotification('Excel文件导出成功！', 'success');
            })
            .catch(error => {
                console.error('导出失败:', error);

                // 恢复按钮状态
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;

                // 显示错误提示
                showNotification('导出失败，请重试', 'error');
            });
        }

        // 处理文件上传
        function handleFileUpload(input) {
            const file = input.files[0];
            if (!file) return;

            // 检查文件类型
            const allowedTypes = ['.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(fileExtension)) {
                alert('请选择Excel文件（.xlsx或.xls格式）');
                input.value = '';
                return;
            }

            // 直接上传并分析
            uploadAndAnalyze(file);
        }

        // 上传文件并直接分析
        function uploadAndAnalyze(file) {
            const formData = new FormData();
            formData.append('file', file);

            // 显示处理状态
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('exportBtn').style.display = 'none';

            // 更新文件信息
            document.getElementById('currentFileName').textContent = `📤 ${file.name} (上传中...)`;
            document.getElementById('dataStats').textContent = '正在处理...';

            fetch('/api/frequent_outage/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新文件信息显示
                    document.getElementById('currentFileName').textContent = `📤 ${file.name}`;

                    if (data.analysis_data) {
                        // 直接显示分析结果
                        displayResults(data.analysis_data);

                        // 更新统计信息
                        const records = data.analysis_data.records || [];
                        const summary = data.analysis_data.summary || {};
                        document.getElementById('dataStats').textContent =
                            `${records.length}条分析结果 | ${summary.total_outages || 0}次瞬断 | ${summary.total_sites || 0}个站点`;

                        showNotification('📤 上传并分析完成！', 'success');

                        // 自动设置日期范围
                        if (data.date_range) {
                            document.getElementById('startDate').value = data.date_range.start;
                            document.getElementById('endDate').value = data.date_range.end;
                        }
                    } else if (data.analysis_error) {
                        document.getElementById('dataStats').textContent = '分析失败';
                        showNotification('文件上传成功，但分析失败: ' + data.analysis_error, 'error');
                    } else {
                        document.getElementById('dataStats').textContent = '上传成功，等待分析';
                        showNotification('📤 文件上传成功！可以开始分析', 'success');
                    }
                } else {
                    document.getElementById('currentFileName').textContent = '上传失败';
                    document.getElementById('dataStats').textContent = '';
                    showNotification('上传失败: ' + data.error, 'error');
                }

                // 隐藏加载动画
                document.getElementById('loadingSpinner').style.display = 'none';
            })
            .catch(error => {
                console.error('上传失败:', error);
                document.getElementById('loadingSpinner').style.display = 'none';
                showNotification('上传失败，请重试', 'error');
            });
        }

        // 显示通知
        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                ${message}
                <button type="button" class="close" onclick="this.parentElement.remove()">
                    <span>&times;</span>
                </button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动消失
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // 获取瞬断次数的样式类
        function getOutageBadgeClass(count) {
            if (count >= 5) return 'badge-danger';
            if (count >= 3) return 'badge-warning';
            return 'badge-success';
        }

        // 获取瞬断天数的样式类
        function getDaysBadgeClass(days) {
            if (days >= 4) return 'badge-danger';
            if (days >= 3) return 'badge-warning';
            if (days >= 2) return 'badge-info';
            return 'badge-success';
        }

        // 获取最近瞬断日期的样式类
        function getDateBadgeClass(dateStr) {
            if (dateStr === '未知') return 'badge-secondary';

            try {
                const date = new Date(dateStr);
                const today = new Date();
                const diffTime = today - date;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays <= 1) return 'badge-danger';      // 今天或昨天
                if (diffDays <= 3) return 'badge-warning';     // 3天内
                if (diffDays <= 7) return 'badge-info';        // 一周内
                return 'badge-success';                         // 一周以上
            } catch (e) {
                return 'badge-secondary';
            }
        }
        
        // 获取网络制式的样式类
        function getTechBadgeClass(tech) {
            if (tech && tech.includes('5G')) return 'badge-info';
            if (tech && tech.includes('4G')) return 'badge-success';
            return 'badge-secondary';
        }
        
        // 页面加载完成后自动获取数据时间范围
        document.addEventListener('DOMContentLoaded', function() {
            loadDateRange();
            setupDragAndDrop();
        });

        // 设置拖拽上传功能
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');

            // 拖拽进入
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#28a745';
                uploadArea.style.backgroundColor = '#e8f5e8';
            });

            // 拖拽离开
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = '#f8f9fa';
            });

            // 文件放下
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = '#f8f9fa';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                        uploadAndAnalyze(file);
                    } else {
                        showNotification('请上传Excel文件 (.xlsx/.xls)', 'error');
                    }
                }
            });
        }

        // 自动获取数据时间范围
        function loadDateRange() {
            fetch('/api/get_outage_date_range')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.date_range) {
                        document.getElementById('startDate').value = data.date_range.start;
                        document.getElementById('endDate').value = data.date_range.end;

                        // 更新当前文件信息显示
                        if (data.source === 'file') {
                            const totalDays = data.total_days || 0;
                            const validRecords = data.valid_time_records || 0;

                            document.getElementById('currentFileName').textContent = data.filename;
                            document.getElementById('dataStats').textContent =
                                `${data.total_records}条记录 | 跨度${totalDays}天 | ${data.date_range.start}至${data.date_range.end}`;

                            // 显示详细的数据信息
                            const monthInfo = data.month_distribution ?
                                Object.entries(data.month_distribution).map(([month, count]) => `${month}:${count}条`).join(', ') : '';

                            let infoText = `🎯 已自动识别数据时间范围: ${data.date_range.start} 至 ${data.date_range.end}`;
                            infoText += `\n📊 数据详情: ${data.filename} | ${data.total_records}条记录 | 跨度${totalDays}天`;
                            if (monthInfo) {
                                infoText += `\n📅 月份分布: ${monthInfo}`;
                            }

                            showNotification(infoText, 'success');
                        } else {
                            document.getElementById('currentFileName').textContent = '数据库数据';
                            document.getElementById('dataStats').textContent = `${data.date_range.start}至${data.date_range.end}`;
                            showNotification(`🎯 已自动设置为数据库时间范围: ${data.date_range.start} 至 ${data.date_range.end}`, 'success');
                        }

                        console.log('自动设置时间范围:', data.date_range);
                    } else {
                        console.warn('无法获取数据时间范围:', data.error);
                        document.getElementById('currentFileName').textContent = '未识别到数据文件';
                        document.getElementById('dataStats').textContent = '请上传瞬断数据文件';
                    }
                })
                .catch(error => {
                    console.error('获取时间范围失败:', error);
                    // 保持默认日期
                });
        }
    </script>
</body>
</html>
